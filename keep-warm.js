const https = require("https");

const URL =
  "https://6pl6otxn2kp5e2te2hollaczb40pthsl.lambda-url.ap-southeast-1.on.aws/api/customer/app-settings/live";
const INTERVAL = 4 * 60 * 1000;
const nextPingTime = () => new Date(Date.now() + INTERVAL).toISOString();

function ping() {
  https
    .get(URL, (res) => {
      console.log(
        `[${new Date().toISOString()}] Pinged: ${res.statusCode}. Next ping is at ${nextPingTime()}`,
      );
    })
    .on("error", (err) => {
      console.error(`[${new Date().toISOString()}] Error: ${err.message}`);
    });
}

setInterval(ping, INTERVAL);
ping(); // initial call
