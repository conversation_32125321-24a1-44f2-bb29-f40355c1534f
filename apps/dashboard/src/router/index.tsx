import { ProtectedRoute } from "@/components/authentication/protected-route"
import { CustomerMiniAppLayout } from "@/components/layout/customer-mini-app-layout"
import { MainLayout } from "@/components/layout/main-layout"
import { PublicLayout } from "@/components/layout/public-layout"
import { t } from "i18next"

import { ErrorBoundary } from "@/errors/error-boundary"
import { TeamHome } from "@/routes/teams/team-home/team-home"
import { TeamRedirect } from "@/routes/teams/team-redirect/team-redirect"
import { components } from "@saf/sdk"
import { createBrowserRouter, Navigate, UIMatch } from "react-router-dom"

export const createRouter = () => {
  return createBrowserRouter([
    {
      element: <CustomerMiniAppLayout />,
      errorElement: <ErrorBoundary />,
      children: [
        {
          children: [
            {
              path: "/apps",
              element: <CustomerMiniAppLayout />,
            },
          ],
        },
      ],
    },
    {
      element: <ProtectedRoute />,
      errorElement: <ErrorBoundary />,
      children: [
        {
          path: "/users",
          lazy: () => import("../routes/users/user-list"),
        },
        {
          element: <MainLayout />,
          children: [
            {
              path: "post-categories",
              handle: {
                breadcrumb: () => t("postCategories.domain"),
              },
              children: [
                {
                  path: "",
                  lazy: () => import("../routes/post-categories/post-category-list"),
                },
                {
                  path: "create",
                  lazy: async () => {
                    const { CreateComponent } = await import("../routes/post-categories/post-category-create-edit")

                    return {
                      Component: CreateComponent,
                    }
                  },
                },
                {
                  path: ":postCategoryName",
                  lazy: async () => {
                    const { Component, Breadcrumb, loader } = await import(
                      "../routes/post-categories/post-category-detail"
                    )

                    return {
                      Component,
                      loader,
                      handle: {
                        breadcrumb: (match: UIMatch<components["schemas"]["PostCategory"]>) => (
                          <Breadcrumb {...match} />
                        ),
                      },
                    }
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: async () => {
                        const { EditComponent } = await import("../routes/post-categories/post-category-create-edit")

                        return {
                          Component: EditComponent,
                        }
                      },
                    },
                  ],
                },
              ],
            },
            {
              path: "posts",
              handle: {
                breadcrumb: () => t("posts.domain"),
              },
              children: [
                {
                  path: "",
                  lazy: () => import("../routes/posts/post-list"),
                },
                {
                  path: "create",
                  lazy: async () => {
                    const { CreateComponent } = await import("../routes/posts/post-create-edit")

                    return {
                      Component: CreateComponent,
                    }
                  },
                },
                {
                  path: ":postId",
                  lazy: async () => {
                    const { Component, Breadcrumb, loader } = await import("../routes/posts/post-detail")

                    return {
                      Component,
                      loader,
                      handle: {
                        breadcrumb: (match: UIMatch<components["schemas"]["Post"]>) => <Breadcrumb {...match} />,
                      },
                    }
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: async () => {
                        const { EditComponent } = await import("../routes/posts/post-create-edit")

                        return {
                          Component: EditComponent,
                        }
                      },
                    },
                  ],
                },
              ],
            },
            {
              path: "post-tags",
              handle: {
                breadcrumb: () => t("postTags.domain"),
              },
              children: [
                {
                  path: "",
                  lazy: () => import("../routes/post-tags/post-tag-list"),
                },
                {
                  path: "create",
                  lazy: async () => {
                    const { CreateComponent } = await import("../routes/post-tags/post-tag-create-edit")

                    return {
                      Component: CreateComponent,
                    }
                  },
                },
                {
                  path: ":postTagName",
                  lazy: async () => {
                    const { Component, Breadcrumb, loader } = await import("../routes/post-tags/post-tag-detail")

                    return {
                      Component,
                      loader,
                      handle: {
                        breadcrumb: (match: UIMatch<components["schemas"]["PostTag"]>) => <Breadcrumb {...match} />,
                      },
                    }
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: async () => {
                        const { EditComponent } = await import("../routes/post-tags/post-tag-create-edit")

                        return {
                          Component: EditComponent,
                        }
                      },
                    },
                  ],
                },
              ],
            },
            {
              path: "pages",
              handle: {
                breadcrumb: () => t("pages.domain"),
              },
              children: [
                {
                  path: "",
                  lazy: () => import("../routes/pages/page-list"),
                },
                {
                  path: "create",
                  lazy: async () => {
                    const { CreateComponent } = await import("../routes/pages/page-create-edit")

                    return {
                      Component: CreateComponent,
                    }
                  },
                },
                {
                  path: ":pageId",
                  lazy: async () => {
                    const { Component, Breadcrumb, loader } = await import("../routes/pages/page-detail")

                    return {
                      Component,
                      loader,
                      handle: {
                        breadcrumb: (match: UIMatch<components["schemas"]["Page"]>) => <Breadcrumb {...match} />,
                      },
                    }
                  },
                  children: [
                    {
                      path: "edit",
                      lazy: async () => {
                        const { EditComponent } = await import("../routes/pages/page-create-edit")

                        return {
                          Component: EditComponent,
                        }
                      },
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
    {
      element: <ProtectedRoute />,
      errorElement: <ErrorBoundary />,
      children: [
        {
          path: "/team-not-found",
          lazy: () => import("../routes/teams/team-not-found"),
        },
        {
          path: "/teams?/:teamId?",
          element: <TeamRedirect />,
          children: [
            {
              path: "",
              element: <TeamHome />,
            },
            {
              element: <MainLayout />,
              children: [
                {
                  path: "users",
                  handle: {
                    breadcrumb: () => t("users.domain"),
                  },
                  children: [
                    {
                      path: "",
                      lazy: () => import("../routes/users/user-list"),
                      children: [
                        {
                          path: "invite",
                          lazy: () => import("../routes/users/user-invite"),
                        },
                      ],
                    },
                    {
                      path: ":userId",
                      lazy: async () => {
                        const { Component, Breadcrumb, loader } = await import("../routes/users/user-detail")

                        return {
                          Component,
                          loader,
                          handle: {
                            breadcrumb: (match: UIMatch<components["schemas"]["UserResponse"]>) => (
                              <Breadcrumb {...match} />
                            ),
                          },
                        }
                      },
                      children: [
                        {
                          path: "edit",
                          lazy: () => import("../routes/users/user-edit"),
                        },
                      ],
                    },
                  ],
                },

                {
                  path: "mini-apps",
                  errorElement: <ErrorBoundary />,
                  handle: {
                    breadcrumb: () => "Mini Apps",
                  },
                  children: [
                    {
                      path: "",
                      lazy: () => import("../routes/mini-apps/mini-app-list"),
                      children: [
                        {
                          path: "create",
                          lazy: () => import("../routes/mini-apps/mini-app-create"),
                        },
                      ],
                    },
                    {
                      path: ":miniAppId",
                      handle: {
                        breadcrumb: () => "Detail",
                      },
                      lazy: () => import("../routes/mini-apps/mini-app-detail"),
                      children: [
                        {
                          path: "edit",
                          lazy: () => import("../routes/mini-apps/mini-app-edit"),
                        },
                      ],
                    },
                  ],
                },
              ],
            },
            {
              errorElement: <ErrorBoundary />,
              children: [
                {
                  path: "mini-apps/:miniAppId/versions/:versionId",
                  lazy: () => import("../routes/mini-apps/mini-app-editor"),
                  children: [
                    {
                      path: "",
                      element: <Navigate replace to="design" />,
                    },
                    {
                      path: "data-sources",
                      lazy: () => import("../routes/mini-apps/mini-app-editor-data"),
                      children: [
                        {
                          path: ":dataSourceId?",
                          lazy: () => import("../routes/mini-apps/mini-app-editor-data/data-sources"),
                          children: [
                            {
                              path: "external-apis/:externalApiId?",
                              lazy: () =>
                                import("../routes/mini-apps/mini-app-editor-data/external-apis/api-endpoints"),
                            },
                          ],
                        },
                      ],
                    },
                    {
                      path: "design",
                      lazy: () => import("../routes/mini-apps/mini-app-editor-design"),
                    },
                    {
                      path: "settings",
                      lazy: () => import("../routes/mini-apps/mini-app-editor-settings"),
                    },
                  ],
                },
              ],
            },
          ],
        },
      ],
    },
    {
      element: <PublicLayout />,
      children: [
        {
          errorElement: <ErrorBoundary />,
          children: [
            {
              path: "/login",
              lazy: () => import("../routes/login"),
            },
            {
              path: "/reset-password",
              lazy: () => import("../routes/reset-password"),
            },
            {
              path: "/invite",
              lazy: () => import("../routes/invite"),
            },
            {
              path: "/create-account",
              lazy: () => import("../routes/create-account"),
            },
            {
              path: "*",
              lazy: () => import("../routes/no-match"),
            },
          ],
        },
      ],
    },
  ])
}
