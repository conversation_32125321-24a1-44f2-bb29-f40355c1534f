import { clx } from "@saf/ui"

export const cn = clx

export const sanitizePage = (page: string | null) => {
  if (!page) return 1
  const pageNumber = parseInt(page)
  if (isNaN(pageNumber)) return 1
  if (pageNumber < 1) return 1
  return pageNumber
}

export const getDeviceName = (userAgent: string) => {
  if (/iPhone/i.test(userAgent)) return "iPhone"
  if (/iPad/i.test(userAgent)) return "iPad"
  if (/Android/i.test(userAgent)) return "Android Device"
  if (/Windows/i.test(userAgent)) return "Windows PC"
  if (/Macintosh|MacIntel/i.test(userAgent)) return "Mac"
  if (/Linux/i.test(userAgent)) return "Linux PC"
  if (/CrOS/i.test(userAgent)) return "Chromebook"

  return ""
}

export const reduceOptionsToMap = <T extends { value: string | number }>(options: T[]) => {
  return options.reduce(
    (map, option) => {
      ;(map as Record<string | number, T>)[option.value] = option
      return map
    },
    {} as Record<T["value"], T>,
  )
}

export function isNumeric(str: any): str is number {
  // The first [!isNaN] is to make sure "13 Abc" doesn't pass this check since parseFloat("13 Abc") will yields 13
  return !isNaN(str) && !isNaN(parseFloat(str))
}

// Utility functions for converting between record and array formats
export const recordToKeyValueArray = <T = string>(record?: Record<string, T>) => {
  if (!record) return []
  return Object.entries(record).map(([key, value]) => ({ key, value }))
}

export const keyValueArrayToRecord = <T = string>(array?: { key: string; value: T }[]) => {
  if (!array || array.length === 0) return undefined

  const record: Record<string, T> = {}
  array.forEach((item) => {
    if (item.key && item.value !== undefined && item.value !== null && item.value !== "") {
      record[item.key] = item.value
    }
  })

  return Object.keys(record).length > 0 ? record : undefined
}

export const parseJSONValues = <T extends Record<string, string>>(
  obj: T,
): { [K in keyof T]: string | number | boolean | null } =>
  Object.fromEntries(
    Object.entries(obj).map(([k, v]) => {
      try {
        return [k, JSON.parse(v)]
      } catch {
        return [k, v]
      }
    }),
  ) as { [K in keyof T]: string | number | boolean | null }

export const convertRecordValuesToString = <T extends Record<string, any>>(obj: T): { [K in keyof T]: string } =>
  Object.fromEntries(Object.entries(obj).map(([k, v]) => [k, `${v}`])) as { [K in keyof T]: string }

export function isNumericString(str: any): str is string {
  return typeof str === "string" && str.trim() !== "" && !isNaN(Number(str))
}

export function resolveNumberOrUndefined(value: any): number | undefined {
  if (typeof value === "number") {
    return value
  }
  if (typeof value === "string" && isNumericString(value)) {
    return Number(value)
  }
  return undefined
}
