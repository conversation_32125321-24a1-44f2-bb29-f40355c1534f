import { safQuery } from "@/client"
import { components } from "@saf/sdk"
import { UIMatch } from "react-router-dom"

type PageDetailBreadcrumbProps = UIMatch<components["schemas"]["Page"]>

export const PageDetailBreadcrumb = (props: PageDetailBreadcrumbProps) => {
  const { pageId } = props.params || {}

  const pageQuery = safQuery.useQuery(
    "get",
    "/api/admin/pages/{pageId}",
    {
      params: {
        path: {
          pageId: parseInt(pageId || ""),
        },
      },
    },
    {
      initialData: props.data,
      enabled: pageId != null,
    },
  )

  const { data: page } = pageQuery

  if (!page) {
    return null
  }

  const display = page.title || ""

  return <span>{display}</span>
}
