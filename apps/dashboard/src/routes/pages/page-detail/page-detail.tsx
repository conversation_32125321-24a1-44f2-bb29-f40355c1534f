import { useLoaderData, useParams } from "react-router-dom"

import { PageGeneralSection } from "./components/page-general-section"
import { pageLoader } from "./loader"

import { safQuery } from "@/client"
import { SingleColumnPageSkeleton } from "../../../components/common/skeleton"
import { SingleColumnPage } from "../../../components/layout/pages"

export const PageDetail = () => {
  const initialData = useLoaderData() as Awaited<ReturnType<typeof pageLoader>>

  const { pageId } = useParams() || {}

  const pageQuery = safQuery.useQuery(
    "get",
    "/api/admin/pages/{pageId}",
    {
      params: {
        path: {
          pageId: parseInt(pageId || ""),
        },
      },
    },
    {
      initialData: initialData,
      enabled: pageId != null,
    },
  )

  const { data: page, isLoading, isError, error } = pageQuery

  if (isLoading || !page) {
    return <SingleColumnPageSkeleton sections={5} />
  }

  if (isError) {
    throw error
  }

  return (
    <SingleColumnPage data={page}>
      <PageGeneralSection page={page} />
    </SingleColumnPage>
  )
}
