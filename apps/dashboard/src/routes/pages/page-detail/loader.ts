import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { LoaderFunctionArgs } from "react-router-dom"

export const pageLoader = async ({ params }: LoaderFunctionArgs) => {
  const { pageId } = params || {}

  const pageQueryOption = safQuery.queryOptions("get", "/api/admin/pages/{pageId}", {
    params: {
      path: {
        pageId: parseInt(pageId || ""),
      },
    },
  })

  return queryClient.ensureQueryData(pageQueryOption)
}
