import { safQuery } from "@/client"
import { SectionRow } from "@/components/common/section"
import { useDate } from "@/hooks/use-date"
import { components } from "@saf/sdk"
import { Button, Container, Heading, toast, usePrompt } from "@saf/ui"
import { useTranslation } from "react-i18next"
import { Link, useNavigate } from "react-router-dom"

type PageGeneralSectionProps = {
  page: components["schemas"]["Page"]
}

export const PageGeneralSection = ({ page }: PageGeneralSectionProps) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const prompt = usePrompt()
  const { getFullDate } = useDate()

  const { mutateAsync: deletePageCategory } = safQuery.useMutation("delete", "/api/admin/pages/{pageId}")

  const title = page.title || ""

  const handleDeletePage = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("pages.deletePageWarning", {
        name: title,
      }),
      verificationText: title,
      verificationInstruction: t("general.typeToConfirm"),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel"),
    })

    if (!res) {
      return
    }

    await deletePageCategory(
      {
        params: {
          path: {
            pageId: page.id,
          },
        },
      },
      {
        onSuccess: () => {
          toast.success(t("pages.deletePageSuccess", { name: page.title }))
          navigate("..")
        },
        onError: (error) => {
          toast.error(error.message)
        },
      },
    )
  }

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading>{page.title}</Heading>
        <div className="flex shrink-0 items-center gap-x-2">
          <Button size="small" variant="secondary" asChild>
            <Link to="edit">{t("edit", "Edit")}</Link>
          </Button>
          <Button size="small" variant="danger" onClick={handleDeletePage}>
            {t("delete", "Delete")}
          </Button>
        </div>
      </div>
      <div className="divide-y">
        <SectionRow title={t("fields.title")} value={page.title || "-"} />
        <SectionRow title={t("fields.content")} value={page.content} type={page.content ? "richText" : ""} />
        <SectionRow title={t("fields.status")} value={page.status} />
        <SectionRow
          title={t("fields.publishDate")}
          value={getFullDate({ date: page.publishedAt || "", includeTime: true })}
        />
        <SectionRow title="Created At" value={getFullDate({ date: page.createdAt })} />
      </div>
    </Container>
  )
}
