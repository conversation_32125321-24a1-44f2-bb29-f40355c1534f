import { safQuery } from "@/client"
import { RouteFocusModalError } from "@/components/common/error-result"
import { ModalFormSectionSkeleton } from "@/components/common/skeleton"
import { RouteDrawer } from "@/components/modals"
import { Heading } from "@saf/ui"
import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"
import { PageForm } from "./components"

export const PageEdit = () => {
  const { t } = useTranslation()
  const { pageId = "" } = useParams()

  const pageQuery = safQuery.useQuery("get", "/api/admin/pages/{pageId}", {
    params: {
      path: {
        pageId: parseInt(pageId || ""),
      },
    },
  })

  const { data, isLoading, error } = pageQuery

  return (
    <RouteDrawer>
      <RouteDrawer.Header>
        <Heading>{t("pages.editPage")}</Heading>
      </RouteDrawer.Header>
      {isLoading ? (
        <ModalFormSectionSkeleton fieldCount={2} />
      ) : error || !data ? (
        <RouteFocusModalError message={error?.message} />
      ) : (
        <PageForm page={data} />
      )}
    </RouteDrawer>
  )
}
