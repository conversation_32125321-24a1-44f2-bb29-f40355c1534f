import { RouteDrawer } from "@/components/modals"
import { Heading } from "@saf/ui"
import { useTranslation } from "react-i18next"
import { PageForm } from "./components"

export const PageCreate = () => {
  const { t } = useTranslation()

  return (
    <RouteDrawer>
      <RouteDrawer.Header>
        <Heading>{t("pages.createPage")}</Heading>
      </RouteDrawer.Header>
      <PageForm />
    </RouteDrawer>
  )
}
