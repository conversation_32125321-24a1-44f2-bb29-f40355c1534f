import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { Button, Input } from "@saf/ui"
import { useForm } from "react-hook-form"
import { useTranslation } from "react-i18next"
import * as zod from "zod"

import { safQuery } from "@/client"
import { Form } from "@/components/common/form"
import { KeyboundForm } from "@/components/common/keybound-form"
import { RouteDrawer, useRouteModal } from "@/components/modals"
import { components } from "@saf/sdk"
import { RichText } from "@/components/inputs/rich-text"

type PageFormProps = {
  page?: components["schemas"]["Page"]
}

const PageFormSchema = zod.object({
  title: zod.string(),
  content: zod.string(),
})

export const PageForm = ({ page }: PageFormProps) => {
  const { t } = useTranslation()
  const { handleSuccess } = useRouteModal()

  const form = useForm<zod.infer<typeof PageFormSchema>>({
    defaultValues: {
      title: page?.title || "",
      content: page?.content,
    },
    resolver: zodResolver(PageFormSchema),
  })

  const { mutateAsync: createMutateAsync, isPending: isCreatePending } = safQuery.useMutation(
    "post",
    "/api/admin/pages",
  )
  const { mutateAsync: updateMutateAsync, isPending: isUpdatePending } = safQuery.useMutation(
    "patch",
    "/api/admin/pages/{pageId}",
  )

  const handleSubmit = form.handleSubmit(async (values) => {
    page == undefined
      ? await createMutateAsync(
          {
            body: values,
          },
          {
            onSuccess: () => {
              handleSuccess()
            },
          },
        )
      : await updateMutateAsync(
          {
            params: {
              path: {
                pageId: page.id,
              },
            },
            body: values,
          },
          {
            onSuccess: () => {
              handleSuccess()
            },
          },
        )
  })

  return (
    <RouteDrawer.Form form={form}>
      <KeyboundForm onSubmit={handleSubmit} className="flex flex-1 flex-col overflow-hidden">
        <RouteDrawer.Body className="flex max-w-full flex-1 flex-col gap-y-8 overflow-y-auto">
          <Form.Field
            control={form.control}
            name="title"
            render={({ field }) => {
              return (
                <Form.Item>
                  <Form.Label>{t("fields.title")}</Form.Label>
                  <Form.Control>
                    <Input {...field} />
                  </Form.Control>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />
          <Form.Field
            control={form.control}
            name="content"
            render={({ field }) => {
              return (
                <Form.Item>
                  <Form.Label>{t("fields.content")}</Form.Label>
                  <Form.Control>
                    <RichText strContent={field.value} onChange={(value) => form.setValue("content", value)} />
                  </Form.Control>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />
        </RouteDrawer.Body>
        <RouteDrawer.Footer>
          <div className="flex items-center justify-end gap-x-2">
            <RouteDrawer.Close asChild>
              <Button size="small" variant="secondary">
                {t("actions.cancel")}
              </Button>
            </RouteDrawer.Close>
            <Button size="small" type="submit" isLoading={page == undefined ? isCreatePending : isUpdatePending}>
              {t("actions.save")}
            </Button>
          </div>
        </RouteDrawer.Footer>
      </KeyboundForm>
    </RouteDrawer.Form>
  )
}
