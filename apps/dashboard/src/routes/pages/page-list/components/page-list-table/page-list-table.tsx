import { Container, createDataTableColumnHelper, createDataTableFilterHelper } from "@saf/ui"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"
import { useNavigate } from "react-router-dom"

import { safQuery } from "@/client"
import { DataTable, emptyDataTableValue } from "@/components/data-table"
import { usePaginationQueryParam } from "@/hooks/use-pagination-query-params"
import { PencilSquare } from "@medusajs/icons"
import { components } from "@saf/sdk"
import { keepPreviousData } from "@tanstack/react-query"

type PageResponse = components["schemas"]["Page"]

const PAGE_SIZE = 20
const SEARCH_QUERY_KEY = "title"

export const PageListTable = () => {
  const { t } = useTranslation()
  const queryParams = usePaginationQueryParam([SEARCH_QUERY_KEY, "status"])

  const { data, isError, error, isPending } = safQuery.useQuery(
    "get",
    "/api/admin/pages",
    {
      params: {
        query: queryParams,
      },
    },
    {
      placeholderData: keepPreviousData,
    },
  )

  const columns = useColumns()
  const filters = useFilters()

  if (isError) {
    throw error
  }

  return (
    <Container className="divide-y p-0">
      <DataTable
        data={data?.items || emptyDataTableValue}
        columns={columns}
        getRowId={(row) => `${row.id}`}
        rowCount={data?.totalCount}
        pageSize={PAGE_SIZE}
        heading={t("pages.domain")}
        rowHref={(row) => `${row.id}`}
        isLoading={isPending}
        searchQueryKey={SEARCH_QUERY_KEY}
        action={{
          label: t("actions.create"),
          to: "create",
        }}
        filters={filters}
        emptyState={{
          empty: {
            heading: t("pages.list.empty.heading"),
            description: t("pages.list.empty.description"),
          },
          filtered: {
            heading: t("pages.list.filtered.heading"),
            description: t("pages.list.filtered.description"),
          },
        }}
      />
    </Container>
  )
}

const columnHelper = createDataTableColumnHelper<PageResponse>()
const filterHelper = createDataTableFilterHelper<{ status: string }>()

const useColumns = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()

  return useMemo(
    () => [
      columnHelper.accessor("title", {
        header: t("fields.title"),
        cell: ({ row }) => {
          return row.original.title || "-"
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc"),
      }),
      columnHelper.action({
        actions: [
          [
            {
              icon: <PencilSquare />,
              onClick: (ctx) => {
                navigate(`${ctx.row.original.id}/edit`)
              },
            },
          ],
        ],
      }),
    ],
    [t, navigate],
  )
}

const useFilters = () => {
  const { t } = useTranslation()

  return useMemo(
    () => [
      filterHelper.accessor("status", {
        label: t("fields.status"),
        type: "radio",
        options: [
          {
            label: t("general.published"),
            value: "published",
          },
          {
            label: t("general.draft"),
            value: "draft",
          },
        ],
      }),
    ],
    [t],
  )
}
