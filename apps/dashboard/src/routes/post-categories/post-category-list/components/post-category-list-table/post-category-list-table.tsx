import { But<PERSON>, Container, <PERSON><PERSON><PERSON><PERSON><PERSON>, Input, Popover } from "@saf/ui"
import { useTranslation } from "react-i18next"

import { safQuery } from "@/client"
import { usePaginationQueryParam } from "@/hooks/use-pagination-query-params"
import { components } from "@saf/sdk"
import { keepPreviousData } from "@tanstack/react-query"
import { SortableBaseItem, SortableList, SortableListProps } from "@/components/common/sortable-list"
import { queryClient } from "@/client/react-query"
import { showHumanFriendlyError } from "@/lib/is-validation-error"
import { DotsSix, PencilSquare } from "@medusajs/icons"
import { useNavigate, useSearchParams } from "react-router-dom"
import { cn } from "@/lib/utils"
import { Dispatch, SetStateAction, useEffect, useState } from "react"
import { IconFilter } from "@tabler/icons-react"

type PostCategoryResponse = components["schemas"]["PostCategory"]

const SEARCH_QUERY_KEY = "name"

export const PostCategoryListTable = () => {
  const { t } = useTranslation()
  const queryParams = usePaginationQueryParam([SEARCH_QUERY_KEY, "isEnabled"])

  const navigate = useNavigate()
  const [searchParams, setSearchParams] = useSearchParams()
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [isFilterChildOpen, setIsFilterChildOpen] = useState(false)
  const [value, setValue] = useState("")
  const [debouncedValue, setDebouncedValue] = useState("")

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, 500)

    return () => {
      clearTimeout(handler)
    }
  }, [value])

  useEffect(() => {
    if (debouncedValue) {
      setSearchParams((prev) => {
        if (value) {
          prev.set(SEARCH_QUERY_KEY, value)
        } else {
          prev.delete(SEARCH_QUERY_KEY)
        }

        return prev
      })
    }
  }, [debouncedValue, setSearchParams, value])

  const movePostCategory = safQuery.useMutation("patch", "/api/admin/post-categories/{postCategoryId}/move")
  const { data, isError, error } = safQuery.useQuery(
    "get",
    "/api/admin/post-categories",
    {
      params: {
        query: {
          ...queryParams,
          isEnabled: queryParams.isEnabled == undefined ? undefined : queryParams.isEnabled.includes("true"),
          limit: "200",
        },
      },
    },
    {
      placeholderData: keepPreviousData,
    },
  )

  if (isError) throw error
  if (!data) {
    return (
      <Container className="txt-compact-small-plus flex flex-col space-y-1 border py-6 text-center text-ui-fg-subtle">
        <p>{t("postCategories.list.empty.heading")}</p>
        <p>{t("postCategories.list.empty.description")}</p>
      </Container>
    )
  }

  const sortedItems = data.items.slice().sort((a, b) => (a.position ?? 0) - (b.position ?? 0))

  const handlePositionChange: SortableListProps<SortableBaseItem>["onChange"] = (newArray, dragEndEvent) => {
    const { active, over } = dragEndEvent
    if (!over || active.id === over.id) return

    const originalItems = [...data.items]
    const newIndex = sortedItems.findIndex(({ id }) => id === over.id)

    const detailQueryOption = safQuery.queryOptions("get", "/api/admin/post-categories", {
      params: {
        query: {
          ...queryParams,
          isEnabled: queryParams.isEnabled == undefined ? undefined : queryParams.isEnabled.includes("true"),
          limit: "200",
        },
      },
    })

    queryClient.setQueryData(detailQueryOption.queryKey, (old: PostCategoryResponse) => {
      if (!old) return old
      return {
        ...old,
        items: newArray.map((item, index) => ({
          ...item,
          position: index + 1,
        })),
      }
    })

    movePostCategory.mutate(
      {
        body: {
          targetPosition: newIndex + 1,
        },
        params: {
          path: {
            postCategoryId: parseInt(dragEndEvent.active.id as string),
          },
        },
      },
      {
        onError: (_, req) => {
          queryClient.setQueryData(detailQueryOption.queryKey, (old: PostCategoryResponse) => ({
            ...old,
            items: originalItems,
          }))
          showHumanFriendlyError(req)
        },
      },
    )
  }

  return (
    <Container className="relative p-0">
      <div className="flex flex-row items-center justify-between px-6 py-4">
        <p>{t("postCategories.domain")}</p>
        <div className="flex flex-row items-center space-x-2">
          <PostCategorySearchField
            setValue={setValue}
            placeholder={t("general.search")}
            value={value}
            className="hidden md:block"
          />
          <PostCategoryFilterMenu
            filterList={[t("fields.isEnabled")]}
            handleFilterClicked={() => {
              setIsFilterOpen(!isFilterOpen)
              setIsFilterChildOpen(!isFilterChildOpen)
            }}
          />
          <Button size="small" onClick={() => navigate("create")}>
            {t("actions.create") ?? ""}
          </Button>
        </div>
      </div>
      <PostCategorySearchField
        setValue={setValue}
        placeholder={t("general.search")}
        value={value}
        className="block w-full px-6 pb-4 md:hidden"
      />
      <div className="bg-ui-bg-subtle">
        {isFilterOpen && (
          <div className="flex flex-row items-center space-x-3 border-b">
            <Popover open={isFilterChildOpen} onOpenChange={setIsFilterChildOpen}>
              <Popover.Trigger className="txt-compact-small-plus py-2 pl-6 text-ui-fg-subtle">
                <p className="w-fit rounded border border-dashed bg-ui-bg-component px-3 py-1 active:bg-ui-bg-base-pressed">
                  {t("fields.isEnabled")}
                </p>
              </Popover.Trigger>
              <Popover.Content className="ml-6">
                {[
                  { label: t("general.yes"), value: true },
                  { label: t("general.no"), value: false },
                ].map((e) => (
                  <div
                    className="txt-compact-small-plus flex cursor-pointer flex-row items-center rounded py-1 pl-3 pr-3 hover:bg-ui-bg-base-pressed"
                    onClick={() => {
                      setSearchParams((prev) => {
                        if (e.value != null) {
                          prev.set("isEnabled", e.value == true ? "true" : "false")
                        } else {
                          prev.delete("isEnabled")
                        }

                        return prev
                      })
                      setIsFilterChildOpen(!isFilterChildOpen)
                    }}
                  >
                    <div
                      className={`h-1 w-1 ${searchParams.get("isEnabled") == e.value.toString() ? "bg-current" : "bg-transparent"} mr-4 rounded-full`}
                    ></div>
                    {e.label}
                  </div>
                ))}
              </Popover.Content>
            </Popover>
            <div
              className="txt-compact-small-plus w-fit cursor-pointer rounded px-3 py-1 text-ui-fg-subtle hover:bg-ui-bg-component active:bg-ui-bg-base-pressed"
              onClick={() => {
                setSearchParams((prev) => {
                  prev.delete("isEnabled")
                  prev.delete(SEARCH_QUERY_KEY)

                  return prev
                })
                setIsFilterOpen(!isFilterOpen)
              }}
            >
              {t("actions.clearAll")}
            </div>
          </div>
        )}
        <p className="txt-compact-small-plus px-6 py-3 text-ui-fg-subtle">{t("fields.name")}</p>
      </div>
      <SortableList
        key={data.items.map((i) => i.id).join("-")}
        className="gap-2"
        items={sortedItems}
        onChange={handlePositionChange}
        renderItem={(postCategory) => (
          <SortableList.Item id={postCategory.id}>
            <PostCategoryItem postCategory={postCategory} className="border-b border-ui-border-base" />
          </SortableList.Item>
        )}
      />
    </Container>
  )
}

const PostCategoryItem = ({ postCategory, className }: { postCategory: PostCategoryResponse; className: string }) => {
  const { attributes, listeners, ref } = SortableList.useSortableItemContext()
  const navigate = useNavigate()

  return (
    <div
      className={cn(
        "txt-compact-small-plus flex w-full flex-row items-center justify-between py-2 pl-2 pr-6 text-ui-fg-subtle hover:bg-ui-bg-base-hover",
        className,
      )}
      {...attributes}
      {...listeners}
      ref={ref}
    >
      <div
        className="flex cursor-pointer flex-row items-center space-x-2 transition-fg"
        onClick={() => navigate(postCategory.name)}
      >
        <DotsSix className="text-ui-fg-muted" />
        <p>{postCategory.name}</p>
      </div>
      <IconButton size="small" variant="primary" onClick={() => navigate(`${postCategory.name}/edit`)}>
        <PencilSquare />
      </IconButton>
    </div>
  )
}

const PostCategorySearchField = ({
  placeholder,
  value,
  setValue,
  className,
}: {
  placeholder: string
  value: string
  setValue: Dispatch<SetStateAction<string>>
  className: string
}) => {
  return (
    <div className={className}>
      <Input placeholder={placeholder} type="search" value={value} onChange={(e) => setValue(e.target.value)} />
    </div>
  )
}

const PostCategoryFilterMenu = ({
  filterList,
  handleFilterClicked,
}: {
  filterList: string[]
  handleFilterClicked: () => void
}) => {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <Popover.Trigger asChild>
        <IconButton size="small" variant="primary" onClick={() => setIsOpen(!isOpen)}>
          <IconFilter />
        </IconButton>
      </Popover.Trigger>
      <Popover.Content className="mr-7 bg-ui-bg-base">
        {filterList.map((e) => (
          <div
            className="txt-compact-small-plus cursor-pointer p-2 text-current"
            onClick={() => {
              handleFilterClicked()
              setIsOpen(!isOpen)
            }}
          >
            {e}
          </div>
        ))}
      </Popover.Content>
    </Popover>
  )
}
