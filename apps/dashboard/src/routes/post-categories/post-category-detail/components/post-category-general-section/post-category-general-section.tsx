import { safQuery } from "@/client"
import { SectionRow } from "@/components/common/section"
import { useDate } from "@/hooks/use-date"
import { components } from "@saf/sdk"
import { Button, Container, Heading, toast, usePrompt } from "@saf/ui"
import { useTranslation } from "react-i18next"
import { Link, useNavigate } from "react-router-dom"

type PostCategoryGeneralSectionProps = {
  postCategory: components["schemas"]["PostCategory"]
}

export const PostCategoryGeneralSection = ({ postCategory }: PostCategoryGeneralSectionProps) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const prompt = usePrompt()
  const { getFullDate } = useDate()

  const { mutateAsync: deletePostCategory } = safQuery.useMutation(
    "delete",
    "/api/admin/post-categories/{postCategoryId}",
  )

  const name = postCategory.name || ""

  const handleDeleteUser = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("postCategories.deleteCategoryWarning", {
        name: name,
      }),
      verificationText: name,
      verificationInstruction: t("general.typeToConfirm"),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel"),
    })

    if (!res) {
      return
    }

    await deletePostCategory(
      {
        params: {
          path: {
            postCategoryId: postCategory.id,
          },
        },
      },
      {
        onSuccess: () => {
          toast.success(t("postCategories.deleteCategorySuccess", { name: postCategory.name }))
          navigate("..")
        },
        onError: (error) => {
          toast.error(error.message)
        },
      },
    )
  }

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading>{postCategory.name}</Heading>
        <div className="flex shrink-0 items-center gap-x-2">
          <Button size="small" variant="secondary" asChild>
            <Link to="edit">{t("edit", "Edit")}</Link>
          </Button>
          <Button size="small" variant="danger" onClick={handleDeleteUser}>
            {t("delete", "Delete")}
          </Button>
        </div>
      </div>
      <div className="divide-y">
        <SectionRow title={t("fields.name")} value={postCategory.name || "-"} />
        <SectionRow
          title={t("fields.image")}
          value={postCategory.imageUrl}
          type={postCategory.imageUrl ? "image" : ""}
        />
        <SectionRow title="Created At" value={getFullDate({ date: postCategory.createdAt })} />
      </div>
    </Container>
  )
}
