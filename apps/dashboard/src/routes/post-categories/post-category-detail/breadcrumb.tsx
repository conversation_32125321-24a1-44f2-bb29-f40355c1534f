import { safQuery } from "@/client"
import { components } from "@saf/sdk"
import { UIMatch } from "react-router-dom"

type PostCategoryDetailBreadcrumbProps = UIMatch<components["schemas"]["PostCategory"]>

export const PostCategoryDetailBreadcrumb = (props: PostCategoryDetailBreadcrumbProps) => {
  const { postCategoryId } = props.params || {}

  const postCategoryQuery = safQuery.useQuery(
    "get",
    "/api/admin/post-categories/{postCategoryId}",
    {
      params: {
        path: {
          postCategoryId: parseInt(postCategoryId || ""),
        },
      },
    },
    {
      initialData: props.data,
      enabled: postCategoryId != null,
    },
  )

  const { data: ostCategory } = postCategoryQuery

  if (!ostCategory) {
    return null
  }

  const display = ostCategory.name || ""

  return <span>{display}</span>
}
