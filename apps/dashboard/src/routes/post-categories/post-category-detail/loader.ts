import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { LoaderFunctionArgs } from "react-router-dom"

export const postCategoryLoader = async ({ params }: LoaderFunctionArgs) => {
  const { postCategoryName } = params || {}

  const userQueryOption = safQuery.queryOptions("get", "/api/admin/post-categories", {
    query: {
      name: {
        postCategoryName: postCategoryName || "",
      },
    },
  })

  return queryClient.ensureQueryData(userQueryOption)
}
