import { useLoaderData, useParams } from "react-router-dom"

import { PostCategoryGeneralSection } from "./components/post-category-general-section"
import { postCategoryLoader } from "./loader"

import { safQuery } from "@/client"
import { SingleColumnPageSkeleton } from "../../../components/common/skeleton"
import { SingleColumnPage } from "../../../components/layout/pages"

export const PostCategoryDetail = () => {
  const initialData = useLoaderData() as Awaited<ReturnType<typeof postCategoryLoader>>

  const { postCategoryName } = useParams() || {}

  const postCategoryQuery = safQuery.useQuery(
    "get",
    "/api/admin/post-categories",
    {
      query: {
        name: {
          postCategoryName: postCategoryName || "",
        },
      },
    },
    {
      initialData: initialData,
      enabled: postCategoryName != null,
    },
  )

  const { data: postCategory, isLoading, isError, error } = postCategoryQuery

  if (isLoading || !postCategory) {
    return <SingleColumnPageSkeleton sections={3} />
  }

  if (isError) {
    throw error
  }

  return (
    <SingleColumnPage data={postCategory.items[0]}>
      <PostCategoryGeneralSection postCategory={postCategory.items[0]} />
    </SingleColumnPage>
  )
}
