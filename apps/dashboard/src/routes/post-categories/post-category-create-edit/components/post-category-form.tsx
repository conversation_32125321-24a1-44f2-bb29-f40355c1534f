import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { Button, IconButton, Input } from "@saf/ui"
import { useForm } from "react-hook-form"
import { useTranslation } from "react-i18next"
import * as zod from "zod"

import { safQuery } from "@/client"
import { Form } from "@/components/common/form"
import { KeyboundForm } from "@/components/common/keybound-form"
import { RouteDrawer, useRouteModal } from "@/components/modals"
import { components } from "@saf/sdk"
import { useState } from "react"
import { FileUpload } from "@/components/common/file-upload"
import { SwitchBox } from "@/components/common/switch-box"
import { XCircle } from "@medusajs/icons"

type PostCategoryFormProps = {
  postCategory?: components["schemas"]["PostCategory"]
}

const PostCategoryFormSchema = zod.object({
  name: zod.string(),
  position: zod.number().optional(),
  isEnabled: zod.boolean(),
  image: zod
    .instanceof(File)
    .refine((file) => file.size < 5 * 1024 * 1024, "Image must be < 5MB")
    .optional(),
})

export const PostCategoryForm = ({ postCategory }: PostCategoryFormProps) => {
  const { t } = useTranslation()
  const { handleSuccess } = useRouteModal()
  const [defaultImage, setDefaultImage] = useState(postCategory?.imageUrl)

  const form = useForm<zod.infer<typeof PostCategoryFormSchema>>({
    defaultValues: {
      name: postCategory?.name || "",
      position: postCategory?.position || 1,
      isEnabled: postCategory?.isEnabled || true,
      image: undefined,
    },
    resolver: zodResolver(PostCategoryFormSchema),
  })

  const { mutateAsync: createMutateAsync, isPending: isCreatePending } = safQuery.useMutation(
    "post",
    "/api/admin/post-categories",
  )
  const { mutateAsync: updateMutateAsync, isPending: isUpdatePending } = safQuery.useMutation(
    "patch",
    "/api/admin/post-categories/{postCategoryId}",
  )

  const handleSubmit = form.handleSubmit(async (values) => {
    const formData = new FormData()
    formData.append("name", values.name)
    if (values.position) formData.append("position", values.position.toString())
    if (values.isEnabled) formData.append("isEnabled", values.isEnabled.toString())
    if (values.image) formData.append("image", values.image)

    postCategory == undefined
      ? await createMutateAsync(
          {
            body: formData as any,
          },
          {
            onSuccess: () => {
              handleSuccess()
            },
          },
        )
      : await updateMutateAsync(
          {
            params: {
              path: {
                postCategoryId: postCategory.id,
              },
            },
            body: formData as any,
          },
          {
            onSuccess: () => {
              handleSuccess()
            },
          },
        )
  })

  return (
    <RouteDrawer.Form form={form}>
      <KeyboundForm onSubmit={handleSubmit} className="flex flex-1 flex-col overflow-hidden">
        <RouteDrawer.Body className="flex max-w-full flex-1 flex-col gap-y-8 overflow-y-auto">
          <Form.Field
            control={form.control}
            name="name"
            render={({ field }) => {
              return (
                <Form.Item>
                  <Form.Label>{t("fields.name")}</Form.Label>
                  <Form.Control>
                    <Input {...field} />
                  </Form.Control>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />
          {defaultImage ? (
            <div className="relative">
              <img src={defaultImage} />
              <IconButton className="absolute right-4 top-2" onClick={() => setDefaultImage(undefined)}>
                <XCircle />
              </IconButton>
            </div>
          ) : (
            <Form.Field
              control={form.control}
              name="image"
              render={({ field }) => {
                return (
                  <Form.Item>
                    <Form.Label>{t("fields.image")}</Form.Label>
                    <Form.Control>
                      <FileUpload
                        {...field}
                        label="Image"
                        multiple={false}
                        onUploaded={(files) => {
                          form.setValue("image", files[0].file)
                        }}
                        onRemove={() => {
                          form.setValue("image", undefined)
                        }}
                        formats={[
                          ".jpg",
                          ".jpeg",
                          ".png",
                          ".gif",
                          ".webp",
                          ".bmp",
                          ".tiff",
                          ".tif",
                          ".svg",
                          ".heic",
                          ".heif",
                          ".ico",
                        ]}
                      />
                    </Form.Control>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />
          )}
          <Form.Field
            control={form.control}
            name="isEnabled"
            render={({ field }) => {
              return (
                <Form.Item>
                  <Form.Control>
                    <SwitchBox label={t("fields.isEnabled")} description={""} {...field} />
                  </Form.Control>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />
        </RouteDrawer.Body>
        <RouteDrawer.Footer>
          <div className="flex items-center justify-end gap-x-2">
            <RouteDrawer.Close asChild>
              <Button size="small" variant="secondary">
                {t("actions.cancel")}
              </Button>
            </RouteDrawer.Close>
            <Button
              size="small"
              type="submit"
              isLoading={postCategory == undefined ? isCreatePending : isUpdatePending}
            >
              {t("actions.save")}
            </Button>
          </div>
        </RouteDrawer.Footer>
      </KeyboundForm>
    </RouteDrawer.Form>
  )
}
