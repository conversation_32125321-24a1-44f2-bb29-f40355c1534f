import { safQuery } from "@/client"
import { RouteFocusModalError } from "@/components/common/error-result"
import { ModalFormSectionSkeleton } from "@/components/common/skeleton"
import { RouteDrawer } from "@/components/modals"
import { Heading } from "@saf/ui"
import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"
import { PostCategoryForm } from "./components/post-category-form"

export const PostCategoryEdit = () => {
  const { t } = useTranslation()
  const { postCategoryName = "" } = useParams()

  const postCategoryQuery = safQuery.useQuery("get", "/api/admin/post-categories", {
    query: {
      name: {
        postCategoryName: postCategoryName || "",
      },
    },
  })

  const { data, isLoading, error } = postCategoryQuery

  return (
    <RouteDrawer>
      <RouteDrawer.Header>
        <Heading>{t("postCategories.editCategory")}</Heading>
      </RouteDrawer.Header>
      {isLoading ? (
        <ModalFormSectionSkeleton fieldCount={2} />
      ) : error || !data ? (
        <RouteFocusModalError message={error?.message} />
      ) : (
        <PostCategoryForm postCategory={data.items[0]} />
      )}
    </RouteDrawer>
  )
}
