import { RouteDrawer } from "@/components/modals"
import { Heading } from "@saf/ui"
import { useTranslation } from "react-i18next"
import { PostCategoryForm } from "./components/post-category-form"

export const PostCategoryCreate = () => {
  const { t } = useTranslation()

  return (
    <RouteDrawer>
      <RouteDrawer.Header>
        <Heading>{t("postCategories.createCategory")}</Heading>
      </RouteDrawer.Header>
      <PostCategoryForm />
    </RouteDrawer>
  )
}
