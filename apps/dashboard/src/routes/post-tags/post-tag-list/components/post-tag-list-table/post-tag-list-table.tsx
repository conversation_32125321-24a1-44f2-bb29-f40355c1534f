import { But<PERSON>, Container, <PERSON><PERSON><PERSON><PERSON><PERSON>, Input } from "@saf/ui"
import { useTranslation } from "react-i18next"

import { safQuery } from "@/client"
import { usePaginationQueryParam } from "@/hooks/use-pagination-query-params"
import { components } from "@saf/sdk"
import { keepPreviousData } from "@tanstack/react-query"
import { SortableBaseItem, SortableList, SortableListProps } from "@/components/common/sortable-list"
import { queryClient } from "@/client/react-query"
import { showHumanFriendlyError } from "@/lib/is-validation-error"
import { DotsSix, PencilSquare } from "@medusajs/icons"
import { useNavigate, useSearchParams } from "react-router-dom"
import { cn } from "@/lib/utils"
import { Dispatch, SetStateAction, useEffect, useState } from "react"

type PostTagResponse = components["schemas"]["PostTag"]

const SEARCH_QUERY_KEY = "name"

export const PostTagListTable = () => {
  const { t } = useTranslation()
  const queryParams = usePaginationQueryParam([SEARCH_QUERY_KEY, "isEnabled"])

  const navigate = useNavigate()
  const [_, setSearchParams] = useSearchParams()
  const [value, setValue] = useState("")
  const [debouncedValue, setDebouncedValue] = useState("")

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, 500)

    return () => {
      clearTimeout(handler)
    }
  }, [value])

  useEffect(() => {
    if (debouncedValue) {
      setSearchParams((prev) => {
        if (value) {
          prev.set(SEARCH_QUERY_KEY, value)
        } else {
          prev.delete(SEARCH_QUERY_KEY)
        }

        return prev
      })
    }
  }, [debouncedValue, setSearchParams, value])

  const movePostTag = safQuery.useMutation("patch", "/api/admin/post-tags/{postTagId}/move")
  const { data, isError, error } = safQuery.useQuery(
    "get",
    "/api/admin/post-tags",
    {
      params: {
        query: {
          ...queryParams,
          isEnabled: queryParams.isEnabled == undefined ? undefined : queryParams.isEnabled.includes("true"),
          limit: "200",
        },
      },
    },
    {
      placeholderData: keepPreviousData,
    },
  )

  if (isError) throw error
  if (!data) {
    return (
      <Container className="txt-compact-small-plus flex flex-col space-y-1 border py-6 text-center text-ui-fg-subtle">
        <p>{t("postTags.list.empty.heading")}</p>
        <p>{t("postTags.list.empty.description")}</p>
      </Container>
    )
  }

  const sortedItems = data.items.slice().sort((a, b) => (a.position ?? 0) - (b.position ?? 0))

  const handlePositionChange: SortableListProps<SortableBaseItem>["onChange"] = (newArray, dragEndEvent) => {
    const { active, over } = dragEndEvent
    if (!over || active.id === over.id) return

    const originalItems = [...data.items]
    const newIndex = sortedItems.findIndex(({ id }) => id === over.id)

    const detailQueryOption = safQuery.queryOptions("get", "/api/admin/post-tags", {
      params: {
        query: {
          ...queryParams,
          isEnabled: queryParams.isEnabled == undefined ? undefined : queryParams.isEnabled.includes("true"),
          limit: "200",
        },
      },
    })

    queryClient.setQueryData(detailQueryOption.queryKey, (old: PostTagResponse) => {
      if (!old) return old
      return {
        ...old,
        items: newArray.map((item, index) => ({
          ...item,
          position: index + 1,
        })),
      }
    })

    movePostTag.mutate(
      {
        body: {
          targetPosition: newIndex + 1,
        },
        params: {
          path: {
            postTagId: parseInt(dragEndEvent.active.id as string),
          },
        },
      },
      {
        onError: (_, req) => {
          queryClient.setQueryData(detailQueryOption.queryKey, (old: PostTagResponse) => ({
            ...old,
            items: originalItems,
          }))
          showHumanFriendlyError(req)
        },
      },
    )
  }

  return (
    <Container className="relative p-0">
      <div className="flex flex-row items-center justify-between px-6 py-4">
        <p>{t("postTags.domain")}</p>
        <div className="flex flex-row items-center space-x-2">
          <PostTagSearchField
            setValue={setValue}
            placeholder={t("general.search")}
            value={value}
            className="hidden md:block"
          />
          <Button size="small" onClick={() => navigate("create")}>
            {t("actions.create") ?? ""}
          </Button>
        </div>
      </div>
      <PostTagSearchField
        setValue={setValue}
        placeholder={t("general.search")}
        value={value}
        className="block w-full px-6 pb-4 md:hidden"
      />
      <div className="bg-ui-bg-subtle">
        <p className="txt-compact-small-plus px-6 py-3 text-ui-fg-subtle">{t("fields.name")}</p>
      </div>
      <SortableList
        key={data.items.map((i) => i.id).join("-")}
        className="gap-2"
        items={sortedItems}
        onChange={handlePositionChange}
        renderItem={(postTag) => (
          <SortableList.Item id={postTag.id}>
            <PostTagItem postTag={postTag} className="border-b border-ui-border-base" />
          </SortableList.Item>
        )}
      />
    </Container>
  )
}

const PostTagItem = ({ postTag, className }: { postTag: PostTagResponse; className: string }) => {
  const { attributes, listeners, ref } = SortableList.useSortableItemContext()
  const navigate = useNavigate()

  return (
    <div
      className={cn(
        "txt-compact-small-plus flex w-full flex-row items-center justify-between py-2 pl-2 pr-6 text-ui-fg-subtle hover:bg-ui-bg-base-hover",
        className,
      )}
      {...attributes}
      {...listeners}
      ref={ref}
    >
      <div
        className="flex cursor-pointer flex-row items-center space-x-2 transition-fg"
        onClick={() => navigate(postTag.name)}
      >
        <DotsSix className="text-ui-fg-muted" />
        <p>{postTag.name}</p>
      </div>
      <IconButton size="small" variant="primary" onClick={() => navigate(`${postTag.name}/edit`)}>
        <PencilSquare />
      </IconButton>
    </div>
  )
}

const PostTagSearchField = ({
  placeholder,
  value,
  setValue,
  className,
}: {
  placeholder: string
  value: string
  setValue: Dispatch<SetStateAction<string>>
  className: string
}) => {
  return (
    <div className={className}>
      <Input placeholder={placeholder} type="search" value={value} onChange={(e) => setValue(e.target.value)} />
    </div>
  )
}
