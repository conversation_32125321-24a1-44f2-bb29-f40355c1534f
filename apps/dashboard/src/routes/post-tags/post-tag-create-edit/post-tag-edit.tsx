import { safQuery } from "@/client"
import { RouteFocusModalError } from "@/components/common/error-result"
import { ModalFormSectionSkeleton } from "@/components/common/skeleton"
import { RouteDrawer } from "@/components/modals"
import { Heading } from "@saf/ui"
import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"
import { PostTagForm } from "./components/post-tag-form"

export const PostTagEdit = () => {
  const { t } = useTranslation()
  const { postTagName = "" } = useParams()

  const postTagQuery = safQuery.useQuery("get", "/api/admin/post-tags", {
    params: {
      query: {
        name: postTagName || "",
      },
    },
  })

  const { data, isLoading, error } = postTagQuery

  return (
    <RouteDrawer>
      <RouteDrawer.Header>
        <Heading>{t("posts.editPost")}</Heading>
      </RouteDrawer.Header>
      {isLoading ? (
        <ModalFormSectionSkeleton fieldCount={2} />
      ) : error || !data ? (
        <RouteFocusModalError message={error?.message} />
      ) : (
        <PostTagForm postTag={data.items[0]} />
      )}
    </RouteDrawer>
  )
}
