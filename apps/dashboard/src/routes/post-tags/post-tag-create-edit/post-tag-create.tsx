import { RouteDrawer } from "@/components/modals"
import { Heading } from "@saf/ui"
import { useTranslation } from "react-i18next"
import { PostTagForm } from "./components/post-tag-form"

export const PostTagCreate = () => {
  const { t } = useTranslation()

  return (
    <RouteDrawer>
      <RouteDrawer.Header>
        <Heading>{t("postTags.createPostTag")}</Heading>
      </RouteDrawer.Header>
      <PostTagForm />
    </RouteDrawer>
  )
}
