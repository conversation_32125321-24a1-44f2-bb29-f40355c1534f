import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { LoaderFunctionArgs } from "react-router-dom"

export const postTagLoader = async ({ params }: LoaderFunctionArgs) => {
  const { postTagName } = params || {}

  const userQueryOption = safQuery.queryOptions("get", "/api/admin/post-tags", {
    params: {
      query: {
        name: postTagName || "",
      },
    },
  })

  return queryClient.ensureQueryData(userQueryOption)
}
