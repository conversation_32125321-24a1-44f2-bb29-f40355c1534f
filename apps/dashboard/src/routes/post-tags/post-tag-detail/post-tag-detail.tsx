import { useLoaderData, useParams } from "react-router-dom"

import { PostTagGeneralSection } from "./components/post-tag-general-section"
import { postTagLoader } from "./loader"

import { safQuery } from "@/client"
import { SingleColumnPageSkeleton } from "../../../components/common/skeleton"
import { SingleColumnPage } from "../../../components/layout/pages"

export const PostTagDetail = () => {
  const initialData = useLoaderData() as Awaited<ReturnType<typeof postTagLoader>>

  const { postTagName } = useParams() || {}

  const postTagQuery = safQuery.useQuery(
    "get",
    "/api/admin/post-tags",
    {
      params: {
        query: {
          name: postTagName || "",
        },
      },
    },
    {
      initialData: initialData,
      enabled: postTagName != null,
    },
  )

  const { data: postTag, isLoading, isError, error } = postTagQuery

  if (isLoading || !postTag) {
    return <SingleColumnPageSkeleton sections={2} />
  }

  if (isError) {
    throw error
  }

  return (
    <SingleColumnPage data={postTag.items[0]}>
      <PostTagGeneralSection postTag={postTag.items[0]} />
    </SingleColumnPage>
  )
}
