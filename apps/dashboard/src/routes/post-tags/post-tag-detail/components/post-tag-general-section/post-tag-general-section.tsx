import { safQuery } from "@/client"
import { SectionRow } from "@/components/common/section"
import { useDate } from "@/hooks/use-date"
import { components } from "@saf/sdk"
import { Button, Container, Heading, toast, usePrompt } from "@saf/ui"
import { useTranslation } from "react-i18next"
import { Link, useNavigate } from "react-router-dom"

type PostTagGeneralSectionProps = {
  postTag: components["schemas"]["PostTag"]
}

export const PostTagGeneralSection = ({ postTag }: PostTagGeneralSectionProps) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const prompt = usePrompt()
  const { getFullDate } = useDate()

  const { mutateAsync: deletePostTag } = safQuery.useMutation("delete", "/api/admin/post-tags/{postTagId}")

  const title = postTag.name || ""

  const handleDeletePostTag = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("postTags.deletePostTagWarning", {
        name: title,
      }),
      verificationText: title,
      verificationInstruction: t("general.typeToConfirm"),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel"),
    })

    if (!res) {
      return
    }

    await deletePostTag(
      {
        params: {
          path: {
            postTagId: postTag.id,
          },
        },
      },
      {
        onSuccess: () => {
          toast.success(t("postTags.deletePostTagSuccess", { name: postTag.name }))
          navigate("..")
        },
        onError: (error) => {
          toast.error(error.message)
        },
      },
    )
  }

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading>{postTag.name}</Heading>
        <div className="flex shrink-0 items-center gap-x-2">
          <Button size="small" variant="secondary" asChild>
            <Link to="edit">{t("edit", "Edit")}</Link>
          </Button>
          <Button size="small" variant="danger" onClick={handleDeletePostTag}>
            {t("delete", "Delete")}
          </Button>
        </div>
      </div>
      <div className="divide-y">
        <SectionRow title={t("fields.name")} value={postTag.name || "-"} />
        <SectionRow title="Created At" value={getFullDate({ date: postTag.createdAt })} />
      </div>
    </Container>
  )
}
