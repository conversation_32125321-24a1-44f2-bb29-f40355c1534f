import { safQuery } from "@/client"
import { components } from "@saf/sdk"
import { UIMatch } from "react-router-dom"

type PostDetailBreadcrumbProps = UIMatch<components["schemas"]["Post"]>

export const PostDetailBreadcrumb = (props: PostDetailBreadcrumbProps) => {
  const { postId } = props.params || {}

  const postQuery = safQuery.useQuery(
    "get",
    "/api/admin/posts/{postId}",
    {
      params: {
        path: {
          postId: parseInt(postId || ""),
        },
      },
    },
    {
      initialData: props.data,
      enabled: postId != null,
    },
  )

  const { data: post } = postQuery

  if (!post) {
    return null
  }

  const display = post.title || ""

  return <span>{display}</span>
}
