import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { LoaderFunctionArgs } from "react-router-dom"

export const postLoader = async ({ params }: LoaderFunctionArgs) => {
  const { postId } = params || {}

  const userQueryOption = safQuery.queryOptions("get", "/api/admin/posts/{postId}", {
    params: {
      path: {
        postId: parseInt(postId || ""),
      },
    },
  })

  return queryClient.ensureQueryData(userQueryOption)
}
