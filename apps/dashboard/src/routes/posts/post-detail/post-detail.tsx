import { useLoaderData, useParams } from "react-router-dom"

import { PostGeneralSection } from "./components/post-general-section"
import { postLoader } from "./loader"

import { safQuery } from "@/client"
import { SingleColumnPageSkeleton } from "../../../components/common/skeleton"
import { SingleColumnPage } from "../../../components/layout/pages"

export const PostDetail = () => {
  const initialData = useLoaderData() as Awaited<ReturnType<typeof postLoader>>

  const { postId } = useParams() || {}

  const postQuery = safQuery.useQuery(
    "get",
    "/api/admin/posts/{postId}",
    {
      params: {
        path: {
          postId: parseInt(postId || ""),
        },
      },
    },
    {
      initialData: initialData,
      enabled: postId != null,
    },
  )

  const { data: post, isLoading, isError, error } = postQuery

  if (isLoading || !post) {
    return <SingleColumnPageSkeleton sections={4} />
  }

  if (isError) {
    throw error
  }

  return (
    <SingleColumnPage data={post}>
      <PostGeneralSection post={post} />
    </SingleColumnPage>
  )
}
