import { safQuery } from "@/client"
import { SectionRow } from "@/components/common/section"
import { useDate } from "@/hooks/use-date"
import { components } from "@saf/sdk"
import { Button, Container, Heading, toast, usePrompt } from "@saf/ui"
import { useTranslation } from "react-i18next"
import { Link, useNavigate } from "react-router-dom"

type PostGeneralSectionProps = {
  post: components["schemas"]["Post"]
}

export const PostGeneralSection = ({ post }: PostGeneralSectionProps) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const prompt = usePrompt()
  const { getFullDate } = useDate()

  const { mutateAsync: deletePost } = safQuery.useMutation("delete", "/api/admin/posts/{postId}")

  const title = post.title || ""

  const handleDeletePost = async () => {
    const res = await prompt({
      title: t("general.areYouSure"),
      description: t("posts.deletePostWarning", {
        name: title,
      }),
      verificationText: title,
      verificationInstruction: t("general.typeToConfirm"),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel"),
    })

    if (!res) {
      return
    }

    await deletePost(
      {
        params: {
          path: {
            postId: post.id,
          },
        },
      },
      {
        onSuccess: () => {
          toast.success(t("posts.deletePostSuccess", { name: post.title }))
          navigate("..")
        },
        onError: (error) => {
          toast.error(error.message)
        },
      },
    )
  }

  return (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading>{post.title}</Heading>
        <div className="flex shrink-0 items-center gap-x-2">
          <Button size="small" variant="secondary" asChild>
            <Link to="edit">{t("edit", "Edit")}</Link>
          </Button>
          <Button size="small" variant="danger" onClick={handleDeletePost}>
            {t("delete", "Delete")}
          </Button>
        </div>
      </div>
      <div className="divide-y">
        <SectionRow title={t("fields.title")} value={post.title || "-"} />
        <SectionRow
          title={t("fields.content")}
          value={JSON.stringify(post.content)}
          type={post.content ? "richText" : ""}
        />
        <SectionRow title={t("fields.image")} value={post.imageUrl} type={post.imageUrl ? "image" : ""} />
        <SectionRow title="Created At" value={getFullDate({ date: post.createdAt })} />
      </div>
    </Container>
  )
}
