import { zod<PERSON><PERSON><PERSON>ver } from "@hookform/resolvers/zod"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, IconButton, Input, Select } from "@saf/ui"
import { useForm } from "react-hook-form"
import { useTranslation } from "react-i18next"
import * as zod from "zod"

import { safQuery } from "@/client"
import { Form } from "@/components/common/form"
import { KeyboundForm } from "@/components/common/keybound-form"
import { RouteDrawer, useRouteModal } from "@/components/modals"
import { components } from "@saf/sdk"
import { FileUpload } from "@/components/common/file-upload"
import { useState } from "react"
import { Combobox } from "@/components/inputs/combobox"
import { RichText } from "@/components/inputs/rich-text"
import { XCircle } from "@medusajs/icons"

type PostFormProps = {
  post?: components["schemas"]["Post"]
}

const PostFormSchema = zod.object({
  title: zod.string(),
  excerpt: zod.string(),
  content: zod.string().optional(),
  image: zod
    .instanceof(File)
    .refine((file) => file.size < 5 * 1024 * 1024, "Image must be < 5MB")
    .optional(),
  publishedAt: zod.date().optional(),
  postCategory: zod.number().optional(),
  postTags: zod.array(zod.number()).optional(),
})

export const PostForm = ({ post }: PostFormProps) => {
  const { t } = useTranslation()
  const { handleSuccess } = useRouteModal()
  const [defaultImage, setDefaultImage] = useState(post?.imageUrl)

  const form = useForm<zod.infer<typeof PostFormSchema>>({
    defaultValues: {
      title: post?.title || "",
      excerpt: post?.excerpt || "",
      content: post?.content || "",
      image: undefined,
      publishedAt: post?.publishedAt == undefined ? undefined : new Date(post?.publishedAt),
      postCategory: post?.postCategory == undefined ? undefined : parseInt(post?.postCategory?.id),
      postTags: post?.postTags?.map((e) => parseInt(e.id)),
    },
    resolver: zodResolver(PostFormSchema),
  })

  const { data: categoryData } = safQuery.useQuery("get", "/api/admin/post-categories", {}, {})
  const { data: tagData } = safQuery.useQuery("get", "/api/admin/post-tags", {}, {})

  const { mutateAsync: createMutateAsync, isPending: isCreatePending } = safQuery.useMutation(
    "post",
    "/api/admin/posts",
  )
  const { mutateAsync: updateMutateAsync, isPending: isUpdatePending } = safQuery.useMutation(
    "patch",
    "/api/admin/posts/{postId}",
  )

  const handleSubmit = form.handleSubmit(async (values) => {
    const formData = new FormData()
    formData.append("title", values.title)
    formData.append("excerpt", values.excerpt)
    if (values.content) formData.append("content", values.content)
    if (values.image) formData.append("image", values.image)
    if (values.publishedAt) formData.append("publishedAt", values.publishedAt.toISOString())
    if (values.postCategory) formData.append("postCategoryId", values.postCategory.toString())
    if (values.postTags) {
      values.postTags.forEach((tag) => {
        formData.append("postTagIds", tag.toString())
      })
    }

    post == undefined
      ? await createMutateAsync(
          {
            body: formData as any,
          },
          {
            onSuccess: () => {
              handleSuccess()
            },
          },
        )
      : await updateMutateAsync(
          {
            params: {
              path: {
                postId: post.id,
              },
            },
            body: formData as any,
          },
          {
            onSuccess: () => {
              handleSuccess()
            },
          },
        )
  })

  return (
    <RouteDrawer.Form form={form}>
      <KeyboundForm onSubmit={handleSubmit} className="flex flex-1 flex-col overflow-hidden">
        <RouteDrawer.Body className="flex max-w-full flex-1 flex-col gap-y-8 overflow-y-auto">
          <Form.Field
            control={form.control}
            name="title"
            render={({ field }) => {
              return (
                <Form.Item>
                  <Form.Label>{t("fields.title")}</Form.Label>
                  <Form.Control>
                    <Input {...field} />
                  </Form.Control>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />
          <Form.Field
            control={form.control}
            name="excerpt"
            render={({ field }) => {
              return (
                <Form.Item>
                  <Form.Label>{t("fields.excerpt")}</Form.Label>
                  <Form.Control>
                    <Input {...field} />
                  </Form.Control>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />
          <Form.Field
            control={form.control}
            name="content"
            render={({ field }) => {
              return (
                <Form.Item>
                  <Form.Label>{t("fields.content")}</Form.Label>
                  <Form.Control>
                    <RichText
                      strContent={JSON.stringify(field.value)}
                      onChange={(value) => form.setValue("content", value)}
                    />
                  </Form.Control>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />
          {defaultImage ? (
            <div className="relative">
              <img src={defaultImage} />
              <IconButton className="absolute right-4 top-2" onClick={() => setDefaultImage(undefined)}>
                <XCircle />
              </IconButton>
            </div>
          ) : (
            <Form.Field
              control={form.control}
              name="image"
              render={({ field }) => {
                return (
                  <Form.Item>
                    <Form.Label>{t("fields.image")}</Form.Label>
                    <Form.Control>
                      <FileUpload
                        {...field}
                        label="Image"
                        multiple={false}
                        onUploaded={(files) => {
                          form.setValue("image", files[0].file)
                        }}
                        onRemove={() => {
                          form.setValue("image", undefined)
                        }}
                        formats={[
                          ".jpg",
                          ".jpeg",
                          ".png",
                          ".gif",
                          ".webp",
                          ".bmp",
                          ".tiff",
                          ".tif",
                          ".svg",
                          ".heic",
                          ".heif",
                          ".ico",
                        ]}
                      />
                    </Form.Control>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />
          )}
          {categoryData && (
            <Form.Field
              control={form.control}
              name="postCategory"
              render={({ field }) => {
                return (
                  <Form.Item>
                    <Form.Label>{t("fields.content")}</Form.Label>
                    <Form.Control>
                      <Select
                        {...field}
                        value={field.value?.toString()}
                        onValueChange={(value) => {
                          if (value == null || value == "") return
                          form.setValue("postCategory", value == null ? undefined : parseInt(value))
                        }}
                      >
                        <Select.Trigger className="w-full">
                          <Select.Value placeholder="Select post category..." />
                        </Select.Trigger>
                        <Select.Content>
                          {categoryData.items.map((item) => (
                            <Select.Item key={item.id} value={item.id.toString()}>
                              {item.name}
                            </Select.Item>
                          ))}
                        </Select.Content>
                      </Select>
                    </Form.Control>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />
          )}
          {tagData && (
            <Form.Field
              control={form.control}
              name="postTags"
              render={({ field }) => {
                return (
                  <Form.Item>
                    <Form.Label>{t("fields.tag")}</Form.Label>
                    <Form.Control>
                      <Combobox
                        {...field}
                        options={tagData.items.map((e) => {
                          return {
                            label: e.name,
                            value: e.id.toString(),
                          }
                        })}
                        value={field.value?.map(String)}
                        onChange={(value) => {
                          const selected = (value ?? []).map(Number)
                          field.onChange(selected)
                        }}
                      />
                    </Form.Control>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />
          )}
          {post != undefined && post.postStatus != "published" && (
            <Form.Field
              control={form.control}
              name="publishedAt"
              render={({ field }) => {
                return (
                  <Form.Item>
                    <Form.Label>{t("fields.publishDate")}</Form.Label>
                    <Form.Control>
                      <DatePicker
                        {...field}
                        minValue={new Date()}
                        onChange={(value) => form.setValue("publishedAt", value == null ? undefined : value)}
                        granularity="minute"
                      />
                    </Form.Control>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />
          )}
        </RouteDrawer.Body>
        <RouteDrawer.Footer>
          <div className="flex items-center justify-end gap-x-2">
            <RouteDrawer.Close asChild>
              <Button size="small" variant="secondary">
                {t("actions.cancel")}
              </Button>
            </RouteDrawer.Close>
            <Button size="small" type="submit" isLoading={post == undefined ? isCreatePending : isUpdatePending}>
              {t("actions.save")}
            </Button>
          </div>
        </RouteDrawer.Footer>
      </KeyboundForm>
    </RouteDrawer.Form>
  )
}
