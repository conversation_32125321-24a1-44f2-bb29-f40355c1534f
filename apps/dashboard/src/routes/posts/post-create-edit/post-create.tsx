import { RouteDrawer } from "@/components/modals"
import { Heading } from "@saf/ui"
import { useTranslation } from "react-i18next"
import { PostForm } from "./components/post-form"

export const PostCreate = () => {
  const { t } = useTranslation()

  return (
    <RouteDrawer>
      <RouteDrawer.Header>
        <Heading>{t("posts.createPost")}</Heading>
      </RouteDrawer.Header>
      <PostForm />
    </RouteDrawer>
  )
}
