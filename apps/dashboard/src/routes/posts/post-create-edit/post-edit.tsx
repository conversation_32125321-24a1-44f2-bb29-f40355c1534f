import { safQuery } from "@/client"
import { RouteFocusModalError } from "@/components/common/error-result"
import { ModalFormSectionSkeleton } from "@/components/common/skeleton"
import { RouteDrawer } from "@/components/modals"
import { Heading } from "@saf/ui"
import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"
import { PostForm } from "./components/post-form"

export const PostEdit = () => {
  const { t } = useTranslation()
  const { postId = "" } = useParams()

  const postQuery = safQuery.useQuery("get", "/api/admin/posts/{postId}", {
    params: {
      path: {
        postId: parseInt(postId || ""),
      },
    },
  })

  const { data, isLoading, error } = postQuery

  return (
    <RouteDrawer>
      <RouteDrawer.Header>
        <Heading>{t("posts.editPost")}</Heading>
      </RouteDrawer.Header>
      {isLoading ? (
        <ModalFormSectionSkeleton fieldCount={2} />
      ) : error || !data ? (
        <RouteFocusModalError message={error?.message} />
      ) : (
        <PostForm post={data} />
      )}
    </RouteDrawer>
  )
}
