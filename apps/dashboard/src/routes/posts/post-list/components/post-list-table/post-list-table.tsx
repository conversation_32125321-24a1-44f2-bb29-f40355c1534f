import { Container, createDataTableColumnHelper, createDataTableFilterHelper } from "@saf/ui"
import { useMemo } from "react"
import { useTranslation } from "react-i18next"
import { useNavigate } from "react-router-dom"

import { safQuery } from "@/client"
import { DataTable, emptyDataTableValue } from "@/components/data-table"
import { usePaginationQueryParam } from "@/hooks/use-pagination-query-params"
import { PencilSquare } from "@medusajs/icons"
import { components } from "@saf/sdk"
import { keepPreviousData } from "@tanstack/react-query"

type PostResponse = components["schemas"]["Post"]
type PostCategoryListResponse = components["schemas"]["PostCategory"][]
type PostTagListResponse = components["schemas"]["PostTag"][]

const PAGE_SIZE = 20
const SEARCH_QUERY_KEY = "title"

export const PostListTable = () => {
  const { t } = useTranslation()
  const queryParams = usePaginationQueryParam([SEARCH_QUERY_KEY, "postCategoryId", "status", "excerpt", "postTagId"])

  const { data, isError, error, isPending } = safQuery.useQuery(
    "get",
    "/api/admin/posts",
    {
      params: {
        query: {
          ...queryParams,
          postCategoryId: queryParams.postCategoryId ? Number(queryParams.postCategoryId) : undefined,
          postTagId: queryParams.postTagId ? Number(queryParams.postTagId) : undefined,
        },
      },
    },
    {
      placeholderData: keepPreviousData,
    },
  )

  const { data: categoryData } = safQuery.useQuery("get", "/api/admin/post-categories", {}, {})
  const { data: tagData } = safQuery.useQuery("get", "/api/admin/post-tags", {}, {})

  const columns = useColumns()
  const filters = useFilters(
    categoryData == undefined ? [] : categoryData.items,
    tagData == undefined ? [] : tagData.items,
  )

  if (isError) {
    throw error
  }

  return (
    <Container className="divide-y p-0">
      <DataTable
        data={data?.items || emptyDataTableValue}
        columns={columns}
        getRowId={(row) => `${row.id}`}
        rowCount={data?.totalCount}
        pageSize={PAGE_SIZE}
        heading={t("posts.domain")}
        rowHref={(row) => `${row.id}`}
        isLoading={isPending}
        searchQueryKey={SEARCH_QUERY_KEY}
        action={{
          label: t("actions.create"),
          to: "create",
        }}
        filters={filters}
        emptyState={{
          empty: {
            heading: t("posts.list.empty.heading"),
            description: t("posts.list.empty.description"),
          },
          filtered: {
            heading: t("posts.list.filtered.heading"),
            description: t("posts.list.filtered.description"),
          },
        }}
      />
    </Container>
  )
}

const columnHelper = createDataTableColumnHelper<PostResponse>()
const filterHelper = createDataTableFilterHelper<{ status: string; postCategoryId: number; postTagId: number }>()

const useColumns = () => {
  const { t } = useTranslation()
  const navigate = useNavigate()

  return useMemo(
    () => [
      columnHelper.accessor("title", {
        header: t("fields.title"),
        cell: ({ row }) => {
          return row.original.title || "-"
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc"),
      }),
      columnHelper.accessor("postCategory", {
        header: t("fields.category"),
        cell: ({ row }) => {
          return row.original.postCategory?.name || "-"
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc"),
      }),
      columnHelper.accessor("postTags", {
        header: t("fields.tag"),
        cell: ({ row }) => {
          return row.original.postTags?.map((e) => e.name).join(",") ?? "-"
        },
        enableSorting: true,
        sortAscLabel: t("filters.sorting.alphabeticallyAsc"),
        sortDescLabel: t("filters.sorting.alphabeticallyDesc"),
      }),
      columnHelper.action({
        actions: [
          [
            {
              icon: <PencilSquare />,
              onClick: (ctx) => {
                navigate(`${ctx.row.original.id}/edit`)
              },
            },
          ],
        ],
      }),
    ],
    [t, navigate],
  )
}

const useFilters = (categoryList: PostCategoryListResponse, tagList: PostTagListResponse) => {
  const { t } = useTranslation()

  return useMemo(
    () => [
      filterHelper.accessor("status", {
        label: t("fields.status"),
        type: "radio",
        options: [
          {
            label: t("general.published"),
            value: "published",
          },
          {
            label: t("general.draft"),
            value: "draft",
          },
        ],
      }),
      filterHelper.accessor("postCategoryId", {
        label: t("fields.category"),
        type: "radio",
        options: categoryList.map((item) => {
          return {
            label: item.name,
            value: item.id,
          }
        }),
      }),
      filterHelper.accessor("postTagId", {
        label: t("fields.tag"),
        type: "radio",
        options: tagList.map((item) => {
          return {
            label: item.name,
            value: item.id,
          }
        }),
      }),
    ],
    [categoryList, tagList, t],
  )
}
