import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { DataTable } from "@/components/data-table"
import { useDate } from "@/hooks/use-date"
import { PencilSquare, Trash } from "@medusajs/icons"
import { components } from "@saf/sdk"
import { Badge, Container, createDataTableColumnHelper, toast, Tooltip, usePrompt } from "@saf/ui"
import { keepPreviousData } from "@tanstack/react-query"
import { useCallback, useMemo, useState } from "react"
import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"
import { CreateMiniAppVersionForm } from "./mini-app-version-create"
import { showHumanFriendlyError } from "@/lib/is-validation-error"
import { miniAppTypeOptions, miniAppVersionStatusOptions } from "@/configs"
import { reduceOptionsToMap } from "@/lib/utils"
import {
  DataTableStatusCell,
  DataTableStatusCellProps,
} from "@/components/data-table/components/data-table-status-cell/data-table-status-cell"

const PAGE_SIZE = 16

type MiniAppVersion = components["schemas"]["MiniAppVersion"]

const columnHelper = createDataTableColumnHelper<MiniAppVersion>()
const miniAppVersionStatusMap = reduceOptionsToMap(miniAppVersionStatusOptions)
const miniAppTypeMap = reduceOptionsToMap(miniAppTypeOptions)

export const MiniAppVersionList = () => {
  const { t } = useTranslation()
  const prompt = usePrompt()
  const { teamId = "", miniAppId = "" } = useParams()
  const [openCreate, setOpenCreate] = useState(false)
  const [edit, setEdit] = useState<MiniAppVersion | undefined>(undefined)

  const { data, isLoading } = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
          miniAppId: parseInt(miniAppId),
        },
      },
    },
    {
      placeholderData: keepPreviousData,
    },
  )

  const { mutate: deleteMutation } = safQuery.useMutation(
    "delete",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}",
  )

  const handleDelete = useCallback(
    async (version: components["schemas"]["MiniAppVersion"]) => {
      const confirm = await prompt({
        title: t("general.areYouSure"),
        description: t("general.areYouSureDescription", {
          title: version.version,
        }),
        verificationInstruction: t("general.typeToConfirm"),
        verificationText: version.version,
        confirmText: t("actions.delete"),
        cancelText: t("actions.cancel"),
      })

      if (!confirm) {
        return
      }

      deleteMutation(
        {
          params: {
            path: {
              teamId: parseInt(teamId),
              miniAppId: parseInt(miniAppId),
              versionId: version.id,
            },
          },
        },
        {
          onSuccess: () => {
            toast.success(t("operationFeedback.deleteSuccess"))
            queryClient.invalidateQueries(
              safQuery.queryOptions("get", "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions", {
                params: {
                  path: {
                    teamId: parseInt(teamId),
                    miniAppId: parseInt(miniAppId),
                  },
                },
              }),
            )
          },
          onError: (error) => {
            toast.error(showHumanFriendlyError(error))
          },
        },
      )
    },
    [prompt, t, deleteMutation, teamId, miniAppId],
  )

  const { getFullDate } = useDate()

  const columns = useMemo(
    () => [
      columnHelper.accessor("version", {
        header: "Version",
        cell: (info) => info.getValue(),
        maxSize: 40,
      }),
      columnHelper.accessor("miniAppVersionStatus", {
        header: () => t("fields.status"),
        cell: ({ getValue }) => {
          const value = getValue()
          return (
            <DataTableStatusCell
              color={
                {
                  approved: "green",
                  rejected: "red",
                  in_review: "orange",
                  draft: "grey",
                  live: "blue",
                  undefined: "grey",
                }[value] as DataTableStatusCellProps["color"]
              }
            >
              {miniAppVersionStatusMap[value]?.label || t("general.unknown")}
            </DataTableStatusCell>
          )
        },
        maxSize: 32,
      }),
      columnHelper.accessor("miniAppType", {
        header: () => t("fields.type"),
        cell: ({ getValue }) => {
          const value = getValue()
          return <Badge size="2xsmall">{miniAppTypeMap[value]?.label || t("general.unknown")}</Badge>
        },
        maxSize: 32,
      }),
      columnHelper.accessor("releaseNote", {
        header: "Release Note",
        cell: ({ getValue }) => {
          const value = getValue()
          return (
            <Tooltip content={value || ""}>
              <div className="flex h-full w-full items-center overflow-hidden">
                <span className="truncate">{value || "-"}</span>
              </div>
            </Tooltip>
          )
        },
        maxSize: 250,
        minSize: 100,
      }),
      columnHelper.accessor("createdAt", {
        header: t("fields.createdAt"),
        cell: ({ row }) => {
          return (
            <Tooltip
              content={getFullDate({
                date: row.original.createdAt,
                includeTime: true,
              })}
            >
              <span>{getFullDate({ date: row.original.createdAt })}</span>
            </Tooltip>
          )
        },
        maxSize: 55,
      }),
      columnHelper.accessor("updatedAt", {
        header: t("fields.updatedAt"),
        cell: ({ row }) => {
          return (
            <Tooltip
              content={getFullDate({
                date: row.original.updatedAt || "",
                includeTime: true,
              })}
            >
              <span>{getFullDate({ date: row.original.updatedAt || "" })}</span>
            </Tooltip>
          )
        },
        maxSize: 55,
      }),
      columnHelper.action({
        actions: (ctx) => {
          return [
            [
              {
                icon: <PencilSquare />,
                onClick: () => setEdit(ctx.row.original),
              },
              {
                icon: <Trash />,
                onClick: () => handleDelete(ctx.row.original),
              },
            ],
          ]
        },
      }),
    ],
    [getFullDate, handleDelete, t],
  )

  const defaultValue = useMemo(() => [], [])

  return (
    <>
      <Container className="divide-y p-0">
        <DataTable
          data={data?.items || defaultValue}
          columns={columns}
          rowCount={data?.totalCount || 0}
          getRowId={(row) => `${row.id}`}
          rowHref={(row) => `versions/${row.id}`}
          pageSize={PAGE_SIZE}
          isLoading={isLoading}
          heading="Versions"
          enableSearch={false}
          action={{
            label: t("actions.create"),
            onClick: () => {
              setEdit(undefined)
              setOpenCreate(true)
            },
          }}
        />
      </Container>
      <CreateMiniAppVersionForm
        open={openCreate}
        onOpenChange={setOpenCreate}
        onSuccess={() => {
          setEdit(undefined)
          setOpenCreate(false)
        }}
      />
      <CreateMiniAppVersionForm
        key={edit?.id}
        open={edit != null}
        onOpenChange={() => setEdit(undefined)}
        miniAppVersion={edit}
        onSuccess={() => {
          setEdit(undefined)
          setOpenCreate(false)
        }}
      />
    </>
  )
}
