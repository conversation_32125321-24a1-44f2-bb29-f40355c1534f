import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { toast, usePrompt } from "@saf/ui"
import { useTranslation } from "react-i18next"
import { MiniAppVersionDetail, Widget } from "../stores/mini-app-design-store"
import { createMiniAppVersionDetailQueryOptions } from "./use-pages"

export const useAddWidget = () => {
  return safQuery.useMutation(
    "post",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/widgets",
    {
      onSuccess: (newWidget, req) => {
        const detailQueryOption = createMiniAppVersionDetailQueryOptions(req.params.path)

        queryClient.setQueryData(detailQueryOption.queryKey, (old: MiniAppVersionDetail) => ({
          ...old,
          pages:
            old?.pages?.map((page) => {
              if (page.id === req.params.path.pageId) {
                return {
                  ...page,
                  widgets: [...(page.widgets || []), newWidget],
                }
              }
              return page
            }) || [],
        }))
      },
    },
  )
}

export const useUpdateWidget = () => {
  return safQuery.useMutation(
    "patch",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/widgets/{widgetId}",
    {
      onSuccess: (updatedWidget, req) => {
        const { teamId, miniAppId, versionId } = req.params.path
        const detailQueryOption = createMiniAppVersionDetailQueryOptions({
          teamId,
          miniAppId,
          versionId,
        })

        queryClient.setQueryData(detailQueryOption.queryKey, (old: MiniAppVersionDetail) => {
          return {
            ...old,
            pages:
              old?.pages?.map((page) => {
                if (page.id === req.params.path.pageId) {
                  return {
                    ...page,
                    widgets: page.widgets?.map((widget) =>
                      widget.id === req.params.path.widgetId ? updatedWidget : widget,
                    ),
                  }
                }
                return page
              }) || [],
          }
        })
      },
    },
  )
}

export const useMoveWidget = () => {
  return safQuery.useMutation(
    "patch",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/widgets/{widgetId}/move",
  )
}

export const useDeleteWidget = () => {
  return safQuery.useMutation(
    "delete",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/widgets/{widgetId}",
    {
      onSuccess: (_, req) => {
        const { teamId, miniAppId, versionId } = req.params.path
        const detailQueryOption = createMiniAppVersionDetailQueryOptions({
          teamId,
          miniAppId,
          versionId,
        })

        // Invalidate the query to get fresh data & shifted positions
        queryClient.invalidateQueries(detailQueryOption)
      },
    },
  )
}

export const useDeleteWidgetWithConfirm = () => {
  const { t } = useTranslation()
  const prompt = usePrompt()
  const deleteWidget = useDeleteWidget()

  const handleDelete = async ({
    teamId,
    miniAppId,
    versionId,
    pageId,
    widgetId,
    onSuccess,
  }: {
    teamId: string | number
    miniAppId: string | number
    versionId: string | number
    pageId: string | number
    widgetId: string | number
    onSuccess?: () => void
  }) => {
    const confirm = await prompt({
      title: t("general.areYouSure"),
      description: t("general.areYouSureDescription", {
        entity: "Widget",
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel"),
    })

    if (!confirm) {
      return
    }

    deleteWidget.mutate(
      {
        params: {
          path: {
            teamId: parseInt(teamId.toString()),
            miniAppId: parseInt(miniAppId.toString()),
            versionId: parseInt(versionId.toString()),
            pageId: parseInt(pageId.toString()),
            widgetId: parseInt(widgetId.toString()),
          },
        },
      },
      {
        onSuccess: () => {
          toast.success(t("operationFeedback.deleteSuccess"))
          onSuccess?.()
        },
        onError: (error) => {
          toast.error(error.message || "Failed to delete widget")
        },
      },
    )
  }

  return {
    handleDelete,
    isPending: deleteWidget.isPending,
  }
}

export const useDuplicateWidget = () => {
  const addWidget = useAddWidget()

  const duplicateWidget = ({
    widget,
    teamId,
    miniAppId,
    versionId,
    pageId,
    onSuccess,
  }: {
    widget: Widget
    teamId: string | number
    miniAppId: string | number
    versionId: string | number
    pageId: string | number
    onSuccess?: (newWidget: Widget) => void
  }) => {
    // Find all widgets in the same page to determine correct position
    const detailQueryOption = createMiniAppVersionDetailQueryOptions({
      teamId: parseInt(teamId.toString()),
      miniAppId: parseInt(miniAppId.toString()),
      versionId: parseInt(versionId.toString()),
    })

    const currentData = queryClient.getQueryData(detailQueryOption.queryKey) as MiniAppVersionDetail | undefined
    const currentPage = currentData?.pages?.find((p) => p.id === parseInt(pageId.toString()))
    const pageWidgets = currentPage?.widgets || []

    // Find widgets after the current one to shift their positions
    const widgetsAfterCurrent = pageWidgets
      .filter((w) => w.position > widget.position)
      .sort((a, b) => a.position - b.position)

    // Optimistically update positions of subsequent widgets
    if (widgetsAfterCurrent.length > 0) {
      queryClient.setQueryData(detailQueryOption.queryKey, (old: MiniAppVersionDetail) => {
        const updatedPages = old.pages.map((page) => {
          if (page.id === parseInt(pageId.toString())) {
            return {
              ...page,
              widgets: page.widgets?.map((w) => {
                if (w.position > widget.position) {
                  return { ...w, position: w.position + 1 }
                }
                return w
              }),
            }
          }
          return page
        })

        return {
          ...old,
          pages: updatedPages,
        }
      })
    }

    // Add the duplicated widget at position + 1
    addWidget.mutate(
      {
        body: {
          name: `${widget.name} Copy`,
          isHidden: widget.isHidden,
          position: widget.position + 1,
          widgetType: widget.widgetType,
          config: widget.config,
        },
        params: {
          path: {
            teamId: parseInt(teamId.toString()),
            miniAppId: parseInt(miniAppId.toString()),
            versionId: parseInt(versionId.toString()),
            pageId: parseInt(pageId.toString()),
          },
        },
      },
      {
        onSuccess: (newWidget) => {
          // Invalidate the query to get fresh data & shifted positions
          queryClient.invalidateQueries(detailQueryOption)
          onSuccess?.(newWidget)
        },
        onError: (error) => {
          toast.error(error.message || "Failed to duplicate widget")
          // Revert optimistic updates on error
          queryClient.invalidateQueries(detailQueryOption)
        },
      },
    )
  }

  return {
    duplicateWidget,
    isPending: addWidget.isPending,
  }
}
