import { safQuery } from "@/client"
import { queryClient } from "@/client/react-query"
import { toast, usePrompt } from "@saf/ui"
import { useTranslation } from "react-i18next"

export const createMiniAppVersionDetailQueryOptions = ({
  teamId,
  miniAppId,
  versionId,
}: {
  teamId: number
  miniAppId: number
  versionId: number
}) => {
  return safQuery.queryOptions("get", "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}", {
    params: {
      path: {
        teamId: teamId,
        miniAppId: miniAppId,
        versionId: versionId,
      },
    },
  })
}

export const useMiniAppVersionDetail = ({
  teamId,
  miniAppId,
  versionId,
}: {
  teamId: number
  miniAppId: number
  versionId: number
}) => {
  return safQuery.useQuery("get", "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}", {
    params: {
      path: {
        teamId: teamId,
        miniAppId: miniAppId,
        versionId: versionId,
      },
    },
  })
}

type MiniAppVersionDetail = Awaited<ReturnType<typeof useMiniAppVersionDetail>>["data"]

export const useCreatePage = () => {
  return safQuery.useMutation(
    "post",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages",
    {
      onSuccess: (newPage, req) => {
        const detailQueryOption = createMiniAppVersionDetailQueryOptions(req.params.path)

        queryClient.setQueryData(detailQueryOption.queryKey, (old: MiniAppVersionDetail) => ({
          ...old,
          pages: [...(old?.pages || []), newPage],
        }))
      },
    },
  )
}

export const useUpdatePage = () => {
  return safQuery.useMutation(
    "patch",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}",
    {
      onSuccess: (updatedPage, req) => {
        const { teamId, miniAppId, versionId } = req.params.path
        const detailQueryOption = createMiniAppVersionDetailQueryOptions({
          teamId,
          miniAppId,
          versionId,
        })

        queryClient.setQueryData(detailQueryOption.queryKey, (old: MiniAppVersionDetail) => {
          return {
            ...old,
            pages: old?.pages?.map((page) => (page.id === updatedPage.id ? { ...page, ...updatedPage } : page)) || [],
          }
        })
      },
    },
  )
}

export const useMovePage = () => {
  return safQuery.useMutation(
    "patch",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}/move",
  )
}

export const useDeletePage = () => {
  return safQuery.useMutation(
    "delete",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions/{versionId}/pages/{pageId}",
    {
      onSuccess: (_, req) => {
        const { teamId, miniAppId, versionId, pageId } = req.params.path
        const detailQueryOption = createMiniAppVersionDetailQueryOptions({
          teamId,
          miniAppId,
          versionId,
        })

        queryClient.setQueryData(detailQueryOption.queryKey, (old: MiniAppVersionDetail) => ({
          ...old,
          pages: old?.pages?.filter((page) => page.id !== pageId) || [],
        }))
      },
    },
  )
}

export const useDeletePageWithConfirm = () => {
  const { t } = useTranslation()
  const prompt = usePrompt()
  const deletePage = useDeletePage()

  const handleDelete = async ({
    teamId,
    miniAppId,
    versionId,
    pageId,
    onSuccess,
  }: {
    teamId: number | string
    miniAppId: number | string
    versionId: number | string
    pageId: number | string
    onSuccess?: () => void
  }) => {
    const confirm = await prompt({
      title: t("general.areYouSure"),
      description: t("general.areYouSureDescription", {
        entity: "Page",
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel"),
    })

    if (!confirm) {
      return
    }

    deletePage.mutate(
      {
        params: {
          path: {
            teamId: parseInt(teamId.toString()),
            miniAppId: parseInt(miniAppId.toString()),
            versionId: parseInt(versionId.toString()),
            pageId: parseInt(pageId.toString()),
          },
        },
      },
      {
        onSuccess: () => {
          toast.success(t("operationFeedback.deleteSuccess"))
          onSuccess?.()
        },
        onError: (error) => {
          toast.error(error.message || "Failed to delete page")
        },
      },
    )
  }

  return {
    handleDelete,
    isPending: deletePage.isPending,
  }
}
