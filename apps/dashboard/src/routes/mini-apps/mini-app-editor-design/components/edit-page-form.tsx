import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { i18n } from "@/i18n/i18n"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button, Checkbox, Divider, Input, Text, toast, usePrompt } from "@saf/ui"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { useTranslation } from "react-i18next"
import { useDebouncedCallback } from "use-debounce"
import { z } from "zod"
import { useDeletePage, useUpdatePage } from "../hooks/use-pages"
import { Page } from "../stores/mini-app-design-store"
import { Loader } from "@/components/common/loader"
import { Plus, Trash } from "@medusajs/icons"
import { IconButton } from "@saf/ui"
import { useDeepCompareEffect } from "use-deep-compare"

const variableNameRegex = /^[a-zA-Z][a-zA-Z0-9_]*$/

const editPageFormSchema = z.object({
  pageName: z.string().min(1, { message: i18n.t("validation.required") }),
  title: z.string().optional(),
  icon: z.string().optional(),
  config: z
    .object({
      variables: z.record(
        z.string().regex(variableNameRegex, {
          message: "Variable name must start with a letter and contain only letters, numbers, and underscores",
        }),
        z.any(),
      ),
    })
    .optional(),
  hideInNavbar: z.boolean(),
  isHidden: z.boolean(),
})

type EditPageSchema = z.infer<typeof editPageFormSchema>

export const EditPageForm = ({
  teamId,
  miniAppId,
  versionId,
  page,
  onDelete,
}: {
  teamId: string
  miniAppId: string
  versionId: string
  page: Page
  onDelete?: () => void
}) => {
  const updatePage = useUpdatePage()
  const [initialRender, setInitialRender] = useState(true)

  const debouncedUpdate = useDebouncedCallback((values: Partial<EditPageSchema>) => {
    updatePage.mutate(
      {
        body: {
          name: values.pageName,
          title: values.title,
          hideInNavbar: values.hideInNavbar,
          icon: values.icon,
          isHidden: values.isHidden,
          config: values.config,
        },
        params: {
          path: {
            teamId: parseInt(teamId),
            miniAppId: parseInt(miniAppId),
            versionId: parseInt(versionId),
            pageId: page.id,
          },
        },
      },
      {
        onError: (error) => {
          toast.error(error.message || "Failed to update screen")
        },
      },
    )
  }, 500)

  const form = useForm<EditPageSchema>({
    resolver: zodResolver(editPageFormSchema),
    defaultValues: {
      pageName: "",
      title: "",
      icon: "",
      config: {
        variables: {},
      },
      hideInNavbar: false,
      isHidden: false,
    },
  })

  useDeepCompareEffect(() => {
    if (page && initialRender) {
      form.reset({
        pageName: page.name,
        title: page.title || "",
        icon: page.icon || "",
        config: {
          variables: page.config?.variables || {},
        },
        hideInNavbar: page.hideInNavbar,
        isHidden: page.isHidden,
      })
      setInitialRender(false)
    }
  }, [page, form, initialRender])

  useEffect(() => {
    if (!page) return

    const subscription = form.watch((values) => {
      debouncedUpdate({
        pageName: values.pageName,
        title: values.title || "",
        icon: values.icon || "",
        config: {
          variables: values.config?.variables ?? {},
        },
        hideInNavbar: values.hideInNavbar,
        isHidden: values.isHidden,
      })
    })

    return () => subscription.unsubscribe()
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, form])

  const addVariable = () => {
    const variables = form.getValues("config.variables") || {}
    let newKey = "newVariable"

    // Ensure unique key
    if (Object.keys(variables).includes(newKey)) {
      let i = 1
      while (Object.keys(variables).includes(`newVariable${i}`)) {
        i++
      }
      newKey = `newVariable${i}`
    }

    const updatedVariables = { ...variables, [newKey]: "" }
    form.setValue("config.variables", updatedVariables, { shouldDirty: true })
  }

  const removeVariable = (key: string) => {
    const variables = { ...form.getValues("config.variables") }
    delete variables[key]
    form.setValue("config.variables", variables, { shouldDirty: true })
  }

  const updateVariableKey = (oldKey: string, newKey: string) => {
    if (oldKey === newKey) return

    const variables = { ...form.getValues("config.variables") }

    // Don't update if the new key already exists
    if (Object.keys(variables).includes(newKey)) {
      toast.error("Variable name must be unique")
      return
    }

    // Update the key
    const value = variables[oldKey]
    delete variables[oldKey]
    variables[newKey] = value

    form.setValue("config.variables", variables, { shouldDirty: true })
  }

  if (!page) {
    return <Text>Screen not found</Text>
  }

  return (
    <>
      <Form {...form}>
        <form className="flex flex-1 flex-col gap-4 p-4">
          <div className="flex items-center justify-between gap-2">
            <Text className="uppercase" size="small">
              Page Settings
            </Text>
            {updatePage.isPending && <Loader size="small" />}
          </div>
          <Field control={form.control} name="pageName" label="Screen Name">
            <Input size="small" />
          </Field>

          <Field control={form.control} name="title" label="Display Title (optional)">
            <Input size="small" />
          </Field>

          <Field control={form.control} name="icon" label="Icon">
            <Input size="small" placeholder="https://example.com/icon.png" />
          </Field>

          <Form.Field
            control={form.control}
            name="hideInNavbar"
            render={({ field: { value, onChange, ...field } }) => {
              return (
                <Form.Item className="flex flex-row items-center gap-3">
                  <Form.Control>
                    <Checkbox checked={value} onCheckedChange={onChange} {...field} />
                  </Form.Control>
                  <Form.Label>Hide in Navigation Bar</Form.Label>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />

          <Form.Field
            control={form.control}
            name="isHidden"
            render={({ field: { value, onChange, ...field } }) => {
              return (
                <Form.Item className="flex flex-row items-center gap-3">
                  <Form.Control>
                    <Checkbox checked={value} onCheckedChange={onChange} {...field} />
                  </Form.Control>
                  <Form.Label>Hide Screen</Form.Label>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />

          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Text size="small" weight="plus">
                Page Variables
              </Text>
              <Button type="button" size="small" variant="secondary" onClick={addVariable}>
                <Plus className="h-4 w-4" />
                Add Variable
              </Button>
            </div>

            {Object.keys(form.watch("config.variables") || {}).length === 0 && (
              <Text size="small" className="text-ui-fg-subtle">
                No variables defined. Click "Add Variable" to create one.
              </Text>
            )}

            {Object.entries(form.watch("config.variables") || {}).map(([key, _]) => (
              <div key={key} className="flex items-start gap-2">
                <div className="flex-1 space-y-2">
                  <Text size="small" weight="plus">
                    Name
                  </Text>
                  <Input
                    size="small"
                    placeholder="variableName"
                    value={key}
                    onChange={(e) => updateVariableKey(key, e.target.value)}
                  />
                  {!variableNameRegex.test(key) && (
                    <Text size="small" className="text-ui-fg-error">
                      Variable name must start with a letter and contain only letters, numbers, and underscores
                    </Text>
                  )}
                </div>
                <div className="flex-1">
                  <Field control={form.control} name={`config.variables.${key}`} label="Value">
                    <Input size="small" placeholder="value or JSON" />
                  </Field>
                </div>
                <IconButton size="small" variant="transparent" className="mt-6" onClick={() => removeVariable(key)}>
                  <Trash className="h-4 w-4" />
                </IconButton>
              </div>
            ))}
          </div>
        </form>
      </Form>
      <div className="mt-auto">
        <Divider orientation="horizontal" />
        <div className="flex items-center justify-between gap-2 p-4">
          <Text size="small">Danger Zone</Text>
          <DeleteButton
            teamId={parseInt(teamId)}
            miniAppId={parseInt(miniAppId)}
            versionId={parseInt(versionId)}
            pageId={page.id}
            onSuccess={() => {
              onDelete?.()
            }}
          />
        </div>
      </div>
    </>
  )
}

const DeleteButton = ({
  teamId,
  miniAppId,
  versionId,
  pageId,
  onSuccess,
}: {
  teamId: number
  miniAppId: number
  versionId: number
  pageId: number
  onSuccess?: () => void
}) => {
  const { t } = useTranslation()
  const prompt = usePrompt()
  const { mutate, isPending } = useDeletePage()

  const handleDelete = async () => {
    const confirm = await prompt({
      title: t("general.areYouSure"),
      description: t("general.areYouSureDescription", {
        entity: "Page",
      }),
      confirmText: t("actions.delete"),
      cancelText: t("actions.cancel"),
    })

    if (!confirm) {
      return
    }

    mutate(
      {
        params: {
          path: {
            teamId,
            miniAppId,
            versionId,
            pageId,
          },
        },
      },
      {
        onSuccess: () => {
          toast.success(t("operationFeedback.deleteSuccess"))
          onSuccess?.()
        },
        onError: (error) => {
          toast.error(error.message)
        },
      },
    )
  }

  return (
    <Button size="small" variant="danger" onClick={handleDelete} isLoading={isPending}>
      Delete Page
    </Button>
  )
}
