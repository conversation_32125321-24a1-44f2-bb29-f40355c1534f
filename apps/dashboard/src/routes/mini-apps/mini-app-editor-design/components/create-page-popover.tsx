import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { i18n } from "@/i18n/i18n"
import { zodResolver } from "@hookform/resolvers/zod"
import { Button, Input, Popover, Text, toast, useToggleState } from "@saf/ui"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { useCreatePage } from "../hooks/use-pages"

const createPageSchema = z.object({
  pageName: z.string().min(1, { message: i18n.t("validation.required") }),
})

type CreatePageSchema = z.infer<typeof createPageSchema>

export const CreatePagePopover = ({
  teamId,
  miniAppId,
  versionId,
  trigger,
  pageCount,
  onSuccess,
}: {
  teamId: string
  miniAppId: string
  versionId: string
  pageCount: number
  trigger: React.ReactNode
  onSuccess?: (page: any) => void
}) => {
  const [state, _, close, toggle] = useToggleState()
  const createPage = useCreatePage()

  const form = useForm<CreatePageSchema>({
    defaultValues: {
      pageName: "",
    },
    resolver: zodResolver(createPageSchema),
  })

  const handleSubmit = form.handleSubmit((values) => {
    createPage.mutate(
      {
        body: {
          name: values.pageName,
          hideInNavbar: false,
          isHidden: false,
          position: pageCount + 1,
        },
        params: {
          path: {
            teamId: parseInt(teamId),
            miniAppId: parseInt(miniAppId),
            versionId: parseInt(versionId),
          },
        },
      },
      {
        onSuccess: (page) => {
          toast.success("Screen created successfully")
          form.reset()
          close()
          onSuccess?.(page)
        },
        onError: (error) => toast.error(error.message || "Failed to create screen"),
      },
    )
  })

  return (
    <Popover
      open={state}
      onOpenChange={() => {
        if (state) {
          form.reset()
        }
        toggle()
      }}
    >
      {trigger && <Popover.Trigger asChild>{trigger}</Popover.Trigger>}
      <Popover.Content className="w-64 p-3">
        <Form {...form}>
          <form className="flex flex-col gap-3" onSubmit={handleSubmit}>
            <Text>Add New Screen</Text>
            <Field control={form.control} name="pageName">
              <Input size="small" placeholder="Enter page name" className="flex-1" />
            </Field>
            <div className="flex justify-end gap-2">
              <Button size="small" type="submit" disabled={createPage.isPending} isLoading={createPage.isPending}>
                Create
              </Button>
            </div>
          </form>
        </Form>
      </Popover.Content>
    </Popover>
  )
}
