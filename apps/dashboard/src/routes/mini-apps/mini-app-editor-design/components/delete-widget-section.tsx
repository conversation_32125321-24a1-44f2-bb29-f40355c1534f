import { Button, Text } from "@saf/ui"
import { cn } from "@/lib/utils"
import { useDeleteWidgetWithConfirm } from "../hooks/use-widgets"

export const DeleteWidgetSection = ({
  teamId,
  miniAppId,
  versionId,
  pageId,
  widgetId,
  className,
  onSuccess,
}: {
  teamId: string
  miniAppId: string
  versionId: string
  pageId: string
  widgetId: string
  className?: string
  onSuccess?: () => void
}) => {
  const { handleDelete, isPending } = useDeleteWidgetWithConfirm()

  return (
    <div className={cn("flex items-center justify-between gap-2", className)}>
      <Text size="small">Danger Zone</Text>
      <Button
        size="small"
        variant="danger"
        onClick={() =>
          handleDelete({
            teamId,
            miniAppId,
            versionId,
            pageId,
            widgetId,
            onSuccess,
          })
        }
        isLoading={isPending}
      >
        Delete Widget
      </Button>
    </div>
  )
}
