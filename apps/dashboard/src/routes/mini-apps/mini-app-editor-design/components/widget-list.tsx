import { WidgetOption, widgetOptions, widgetOptionsMap } from "@/components/widgets/utils/widget-options"

import { queryClient } from "@/client/react-query"
import { Loader } from "@/components/common/loader"
import { SortableBaseItem, SortableList, SortableListProps } from "@/components/common/sortable-list"
import { showHumanFriendlyError } from "@/lib/is-validation-error"
import { cn } from "@/lib/utils"
import { DotsSix, EllipsisVertical } from "@medusajs/icons"
import { Button, DropdownMenu, IconButton, Input, Popover, Text, toast, Tooltip } from "@saf/ui"
import { IconPlus } from "@tabler/icons-react"
import { useState } from "react"
import { createMiniAppVersionDetailQueryOptions, useMiniAppVersionDetail } from "../hooks/use-pages"
import { useAddWidget, useDeleteWidgetWithConfirm, useDuplicateWidget, useMoveWidget } from "../hooks/use-widgets"
import { MiniAppVersionDetail, Page, useMiniAppDesignStore, Widget } from "../stores/mini-app-design-store"

export const WidgetList = ({
  teamId,
  miniAppId,
  versionId,
}: {
  teamId: string
  miniAppId: string
  versionId: string
}) => {
  const [widgetsPopoverOpen, setWidgetsPopoverOpen] = useState(false)
  const { data, isLoading, error } = useMiniAppVersionDetail({
    teamId: parseInt(teamId),
    miniAppId: parseInt(miniAppId),
    versionId: parseInt(versionId),
  })

  const moveWidget = useMoveWidget()

  const {
    selectedPageId: _selectedPageId,
    selectedWidgetId: _selectedWidgetId,
    setSelectedWidgetId,
  } = useMiniAppDesignStore()

  if (isLoading) {
    return <Loader size="small" />
  }

  if (error) {
    return <p className="text-ui-button-danger">{showHumanFriendlyError(error)}</p>
  }

  if (!data) {
    return <p className="text-ui-fg-muted">No data found</p>
  }

  const selectedPage: Page | undefined = data?.pages?.find((page) => page.id === _selectedPageId) || data.pages[0]

  const pageWidgets = selectedPage != null ? selectedPage.widgets || [] : []
  const selectedWidget: Widget | undefined = pageWidgets.find((widget) => widget.id === _selectedWidgetId)

  const sortedWidgets = pageWidgets.sort((a, b) => a.position - b.position)

  const handlePositionChange: SortableListProps<SortableBaseItem>["onChange"] = (newArray, dragEndEvent) => {
    const { active, over } = dragEndEvent
    if (!over || active.id === over.id || !selectedPage) return

    const originalWidgets = [...pageWidgets]

    const newIndex = sortedWidgets.findIndex(({ id }) => id === over.id)

    const detailQueryOption = createMiniAppVersionDetailQueryOptions({
      teamId: parseInt(teamId),
      miniAppId: parseInt(miniAppId),
      versionId: parseInt(versionId),
    })

    // Optimistically update the query data to reflect the new order
    queryClient.setQueryData(detailQueryOption.queryKey, (old: MiniAppVersionDetail) => {
      const updatedPages = old.pages.map((page) => {
        if (page.id === selectedPage.id) {
          const positionUpdatedWidgets = newArray.map((item, index) => ({
            ...item,
            position: index + 1,
          }))
          return {
            ...page,
            widgets: positionUpdatedWidgets,
          }
        }
        return page
      })

      return {
        ...old,
        pages: updatedPages,
      }
    })

    moveWidget.mutate(
      {
        body: {
          // +1 because positions are 1-based in the API
          targetPosition: newIndex + 1,
        },
        params: {
          path: {
            teamId: parseInt(teamId),
            miniAppId: parseInt(miniAppId),
            versionId: parseInt(versionId),
            pageId: selectedPage.id,
            widgetId: parseInt(dragEndEvent.active.id as string),
          },
        },
      },
      {
        onError: (_, req) => {
          // Revert the optimistic update if the mutation fails
          queryClient.setQueryData(detailQueryOption.queryKey, (old: MiniAppVersionDetail) => {
            const updatedPages = old.pages.map((page) => {
              if (page.id === selectedPage.id) {
                return {
                  ...page,
                  widgets: originalWidgets,
                }
              }
              return page
            })

            return {
              ...old,
              pages: updatedPages,
            }
          })
          showHumanFriendlyError(req)
        },
      },
    )
  }

  return (
    <div className="flex flex-col gap-2">
      <div className="flex items-center justify-between gap-2 px-3.5">
        <Text size="xsmall" className="uppercase" weight="plus">
          Widgets
        </Text>
        <Popover open={widgetsPopoverOpen} onOpenChange={setWidgetsPopoverOpen}>
          <Popover.Trigger asChild>
            <IconButton size="xsmall" variant="primary" disabled={!selectedPage}>
              <IconPlus className="size-4" />
            </IconButton>
          </Popover.Trigger>
          <Popover.Content className="w-64 p-0" side="right" align="end">
            {selectedPage && (
              <ComponentsSelector
                teamId={teamId}
                miniAppId={miniAppId}
                versionId={versionId}
                pageId={`${selectedPage.id}`}
                widgetCount={pageWidgets.length}
                onSuccess={() => setWidgetsPopoverOpen(false)}
              />
            )}
          </Popover.Content>
        </Popover>
      </div>
      <div className="space-y-1.5 px-2 py-1">
        {!selectedPage ? (
          <Text className="px-1.5" size="xsmall" color="subtle">
            Select a page to add widgets
          </Text>
        ) : null}
        {selectedPage && !pageWidgets?.length ? (
          <Text className="px-1.5" size="xsmall" color="subtle">
            Widget added to the page will appear here
          </Text>
        ) : null}
        {selectedPage && pageWidgets.length > 0 && (
          <SortableList
            className="gap-2"
            items={sortedWidgets}
            onChange={handlePositionChange}
            renderItem={(widget) => {
              return (
                <SortableList.Item id={widget.id}>
                  <WidgetItem
                    widget={widget}
                    selectedWidget={selectedWidget}
                    setSelectedWidgetId={setSelectedWidgetId}
                    teamId={teamId}
                    miniAppId={miniAppId}
                    versionId={versionId}
                    pageId={selectedPage.id?.toString()}
                  />
                </SortableList.Item>
              )
            }}
          />
        )}
      </div>
    </div>
  )
}

export const WidgetItem = ({
  widget,
  selectedWidget,
  setSelectedWidgetId,
  teamId,
  miniAppId,
  versionId,
  pageId,
}: {
  widget: Widget
  selectedWidget: Widget | undefined
  setSelectedWidgetId: (id?: number) => void
  teamId: string
  miniAppId: string
  versionId: string
  pageId: string
}) => {
  const { attributes, listeners, ref } = SortableList.useSortableItemContext()
  const { duplicateWidget, isPending: isDuplicating } = useDuplicateWidget()
  const { handleDelete } = useDeleteWidgetWithConfirm()

  const handleDuplicate = () => {
    duplicateWidget({
      widget,
      teamId,
      miniAppId,
      versionId,
      pageId,
      onSuccess: (newWidget) => {
        setSelectedWidgetId(newWidget.id)
      },
    })
  }

  return (
    <div className="group relative w-full">
      <Button
        type="button"
        variant={widget.id === selectedWidget?.id ? "secondary" : "transparent"}
        key={widget.id}
        className={cn(
          "flex w-full items-center justify-start p-1 pe-2 ps-1 text-start [&>svg]:size-4",
          widget.isHidden && "opacity-50",
        )}
        onClick={() => setSelectedWidgetId(widget.id)}
        {...attributes}
        {...listeners}
        ref={ref}
      >
        <DotsSix className="size-3 shrink-0 cursor-grab touch-none text-ui-fg-muted active:cursor-grabbing" />
        <div className="grid size-6 shrink-0 place-items-center rounded-sm bg-ui-bg-subtle [&>*]:size-4">
          {widgetOptionsMap[widget.widgetType]?.icon}
        </div>
        <Text size="xsmall" leading="compact" className="truncate">
          {widget.name}
        </Text>
        <div className="rounded-full bg-ui-bg-component px-2 py-0.5">
          <Text size="xsmall" leading="compact" className="truncate text-xs text-ui-fg-subtle" color="subtle">
            {widgetOptionsMap[widget.widgetType]?.name}
          </Text>
        </div>
      </Button>
      <div className="absolute right-0 top-1/2 grid -translate-y-1/2 place-items-center">
        <DropdownMenu>
          <DropdownMenu.Trigger asChild>
            <IconButton size="xsmall" variant="transparent" disabled={isDuplicating}>
              <EllipsisVertical className="opacity-30 transition-opacity group-hover:opacity-100" />
            </IconButton>
          </DropdownMenu.Trigger>
          <DropdownMenu.Content align="end">
            <DropdownMenu.Item onClick={handleDuplicate}>Duplicate</DropdownMenu.Item>
            <DropdownMenu.Item
              onClick={() =>
                handleDelete({
                  teamId,
                  miniAppId,
                  versionId,
                  pageId,
                  widgetId: widget.id,
                  onSuccess: () => setSelectedWidgetId(undefined),
                })
              }
            >
              Delete
            </DropdownMenu.Item>
          </DropdownMenu.Content>
        </DropdownMenu>
      </div>
    </div>
  )
}

export const ComponentsSelector = ({
  teamId,
  miniAppId,
  versionId,
  pageId,
  widgetCount,
  onSuccess,
}: {
  teamId: string
  miniAppId: string
  versionId: string
  pageId: string
  widgetCount: number
  onSuccess: () => void
}) => {
  const { setSelectedWidgetId } = useMiniAppDesignStore()
  const addWidget = useAddWidget()
  const [filter, setFilter] = useState("")
  const filteredWidgetOptions = widgetOptions.reduce((acc, group) => {
    const filteredWidgets = group.widgets.filter(
      (widget) =>
        widget.name.toLowerCase().includes(filter.toLowerCase()) ||
        (widget.description && widget.description.toLowerCase().includes(filter.toLowerCase())),
    )
    if (filteredWidgets.length > 0) {
      acc.push({
        label: group.label,
        widgets: filteredWidgets,
      })
    }
    return acc
  }, [] as WidgetOption[])

  const handleAddWidget = (widget: WidgetOption["widgets"]["0"]) => {
    addWidget.mutate(
      {
        body: {
          name: widget.name,
          isHidden: false,
          position: widgetCount + 1,
          widgetType: widget.widgetType,
          config: "",
        },
        params: {
          path: {
            teamId: parseInt(teamId),
            miniAppId: parseInt(miniAppId),
            versionId: parseInt(versionId),
            pageId: parseInt(pageId),
          },
        },
      },
      {
        onSuccess: (newWidget) => {
          setSelectedWidgetId(newWidget.id)
          onSuccess()
        },
        onError: (error) => toast.error(error.message || "Failed to add widget"),
      },
    )
  }
  return (
    <div className="flex h-full max-h-[800px] flex-col divide-y overflow-hidden py-2">
      <div className="px-2 pb-2">
        <Input
          type="search"
          placeholder="Search widgets..."
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
        />
      </div>
      <div className="flex-1 divide-y overflow-y-auto">
        {filteredWidgetOptions.map((widgetGroup) => (
          <div key={widgetGroup.label} className="py-2">
            <Text size="xsmall" className="px-4 uppercase" weight="plus">
              {widgetGroup.label}
            </Text>
            <div className="grid grid-cols-3 gap-2 px-1 py-2">
              {widgetGroup.widgets.map((widget) => (
                <button
                  disabled={addWidget.isPending}
                  type="button"
                  key={widget.name}
                  onClick={() => handleAddWidget(widget)}
                  className={cn(
                    "flex w-full flex-col items-center justify-start gap-1.5 rounded-lg p-2 text-center transition-colors hover:bg-ui-bg-base-hover active:bg-ui-bg-base-pressed [&>svg]:size-4",
                  )}
                >
                  <div className="grid size-9 place-items-center rounded-md bg-ui-bg-interactive shadow-sm [&>svg]:size-5 [&>svg]:text-ui-fg-on-color">
                    {widget.icon}
                  </div>
                  <Text size="xsmall" color="subtle">
                    {widget.name}
                  </Text>
                  {widget.description && <Tooltip content={widget.description}>{widget.description}</Tooltip>}
                </button>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
