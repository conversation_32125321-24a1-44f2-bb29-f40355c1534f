import { DevicePreview } from "@/components/device-preview"

import { Loader } from "@/components/common/loader"
import { WidgetEditor } from "@/components/widgets"
import { CustomerMiniAppRoot } from "@/routes/customer-mini-app/customer-mini-app"
import { Container, Divider, Text } from "@saf/ui"
import { IconChevronRight } from "@tabler/icons-react"
import { useParams } from "react-router-dom"
import { DeleteWidgetSection } from "./components/delete-widget-section"
import { EditPageForm } from "./components/edit-page-form"
import { PageList } from "./components/page-list"
import { WidgetList } from "./components/widget-list"
import { useMiniAppVersionDetail } from "./hooks/use-pages"
import { Page, useMiniAppDesignStore, Widget } from "./stores/mini-app-design-store"
import { useEffect } from "react"

export const MiniAppEditorDesign = () => {
  const { teamId = "", miniAppId = "", versionId = "" } = useParams()

  const { data, isLoading, error } = useMiniAppVersionDetail({
    teamId: parseInt(teamId),
    miniAppId: parseInt(miniAppId),
    versionId: parseInt(versionId),
  })

  const {
    selectedPageId: _selectedPageId,
    setSelectedPageId,
    selectedWidgetId: _selectedWidgetId,
    setSelectedWidgetId,
  } = useMiniAppDesignStore()

  useEffect(() => {
    if (data?.pages && data.pages.length > 0 && _selectedPageId == null) {
      setSelectedPageId(data.pages[0].id)
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data])

  if (isLoading || !data) {
    return (
      <div className="grid h-full place-items-center pb-20">
        <Loader size="large" />
      </div>
    )
  }

  if (error) {
    throw error
  }

  const selectedPage: Page | undefined = data?.pages?.find((page) => page.id === _selectedPageId)

  const pageWidgets = selectedPage != null ? selectedPage.widgets || [] : []
  const selectedWidget: Widget | undefined = pageWidgets.find((widget) => widget.id === _selectedWidgetId)

  return (
    <div className="flex h-[calc(100vh-var(--topbar-height))] w-full justify-between gap-2">
      <Container className="m-2 flex w-[240px] flex-col p-0 lg:w-[300px]">
        <div className="max-h-[400px] overflow-y-auto py-3">
          <PageList teamId={teamId} miniAppId={miniAppId} versionId={versionId} />
        </div>
        <Divider variant="dashed" className="shrink-0" />
        <div className="flex-[3] overflow-y-auto py-3">
          <WidgetList teamId={teamId} miniAppId={miniAppId} versionId={versionId} />
        </div>
      </Container>
      <div className="grid place-items-center">
        <DevicePreview>
          <CustomerMiniAppRoot
            data={data}
            focusedPageId={selectedPage?.id}
            onPageChanged={setSelectedPageId}
            focusedWidgetId={selectedWidget?.id}
            onWidgetChanged={setSelectedWidgetId}
          />
        </DevicePreview>
      </div>
      <Container className="m-2 flex w-[260px] flex-col p-0 lg:w-[350px]">
        <div className="border-b px-4 py-3">
          <Text className="uppercase" size="xsmall" weight="plus">
            {selectedPage?.name} {selectedWidget?.name && <IconChevronRight className="inline size-3" />}
            {selectedWidget?.name}
          </Text>
        </div>
        {selectedPage != null && selectedWidget == null ? (
          <EditPageForm
            key={selectedPage.id}
            teamId={teamId}
            miniAppId={miniAppId}
            versionId={versionId}
            page={selectedPage}
          />
        ) : null}
        {selectedPage != null && selectedWidget && (
          <div className="flex flex-1 flex-col overflow-y-auto">
            <WidgetEditor
              key={selectedWidget.id}
              teamId={teamId}
              miniAppId={miniAppId}
              versionId={versionId}
              pageId={`${selectedPage.id}`}
              widget={selectedWidget}
            />
            <div className="mt-auto">
              <Divider />
              <DeleteWidgetSection
                teamId={teamId}
                miniAppId={miniAppId}
                versionId={versionId}
                pageId={`${selectedPage.id}`}
                widgetId={`${selectedWidget.id}`}
                className="p-4"
              />
            </div>
          </div>
        )}
      </Container>
    </div>
  )
}
