import { safQuery } from "@/client"
import { <PERSON>rror<PERSON><PERSON><PERSON> } from "@/components/common/error-result"
import { Loader } from "@/components/common/loader"
import { showHumanFriendlyError } from "@/lib/is-validation-error"
import { components } from "@saf/sdk"
import React from "react"
import { useParams } from "react-router-dom"
import { ApiEndpointCreateForm } from "./api-endpoint-create-form"

type ApiEndpointResponse = components["schemas"]["ExternalApiResponse"]

interface ApiEndpointEditSectionProps {
  baseUrl?: string
  onSuccess?: (data: ApiEndpointResponse) => void
}

export const ApiEndpointEditSection: React.FC<ApiEndpointEditSectionProps> = ({ baseUrl, onSuccess }) => {
  const { teamId = "", miniAppId = "", dataSourceId = "", externalApiId = "" } = useParams()

  const { data, isLoading, error } = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis/{externalApiId}",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
          miniAppId: parseInt(miniAppId),
          dataSourceId: parseInt(dataSourceId),
          externalApiId: parseInt(externalApiId),
        },
      },
    },
    {
      enabled: !!externalApiId && externalApiId !== "create",
    },
  )

  if (isLoading) {
    return (
      <div className="flex h-full items-center justify-center">
        <Loader />
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex h-full items-center justify-center">
        <ErrorResult message={showHumanFriendlyError(error)} />
      </div>
    )
  }

  return <ApiEndpointCreateForm baseUrl={baseUrl} data={data} onSuccess={onSuccess} />
}
