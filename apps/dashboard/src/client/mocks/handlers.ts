import { paths } from "@saf/sdk"
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "msw"
import { createOpenApiHttp } from "openapi-msw"
import { API_URL } from ".."

const openApiHttp = createOpenApiHttp<paths>({
  baseUrl: API_URL,
})

export const handlers: RequestHandler[] = [
  // openApiHttp.post('/api/admin/auth/login', ({ response }) => {
  //   return response(200).json({
  //     accessToken:
  //       'eyJhbGciOiJodHRwOi8vd3d3LnczLm9yZy8yMDAxLzA0L3htbGRzaWctbW9yZSNobWFjLXNoYTI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.n3O_YLhB7hk81T1OpZUrJ7AVmNRJSTv7ztDQLOASTRQ',
  //     refreshToken: 'fbc69367c0034e258d14448df6fce80c',
  //     user: {
  //       id: 8,
  //       name: 'Test Account [Hsing]',
  //       email: '<EMAIL>',
  //       userStatus: 1,
  //       systemRoles: ['system_admin', 'system_editor'],
  //       teams: [
  //         {
  //           id: 1,
  //           name: '69 TEAM [Hsing]',
  //           roles: ['team_admin', 'team_developer'],
  //         },
  //       ],
  //     },
  //   });
  // }),
  // openApiHttp.get('/api/admin/auth/me', ({ response }) => {
  //   return response(200).json({
  //     id: 8,
  //     name: 'Test Account [Hsing]',
  //     email: '<EMAIL>',
  //     userStatus: 1,
  //     systemRoles: ['system_admin', 'system_editor'],
  //     teams: [
  //       {
  //         id: 1,
  //         name: '69 TEAM [Hsing]',
  //         roles: ['team_admin', 'team_developer'],
  //       },
  //     ],
  //   });
  // }),
  openApiHttp.get("/api/admin/teams/{teamId}/mini-apps", ({ response }) => {
    return response(200).json({
      items: [
        {
          id: 1,
          name: "Sample MiniApp",
          description: "This is a sample mini app for testing purposes.",
          customerServiceContactNumber: "123-456-7890",
          customerServiceContactEmail: "<EMAIL>",
          termsAndConditionsUrl: "https://example.com/terms",
          miniAppStatus: "active",
          categoryId: 101,
          teamId: 202,
          createdAt: "2023-01-01T00:00:00Z",
          createdBy: "admin",
          updatedAt: "2023-01-02T00:00:00Z",
          updatedBy: "admin",
        },
      ],
      page: 1,
      pageSize: 10,
      totalCount: 1,
      totalPages: 1,
    })
  }),
  openApiHttp.get("/api/admin/teams/{teamId}/mini-apps/{miniAppId}", ({ params, response }) => {
    const id = parseInt(params.miniAppId as string)
    return response(200).json({
      id: id,
      name: `MiniApp ${id}`,
      description: `This is mini app ${id} for testing purposes.`,
      customerServiceContactNumber: "123-456-7890",
      customerServiceContactEmail: "<EMAIL>",
      termsAndConditionsUrl: "https://example.com/terms",
      miniAppStatus: "active",
      categoryId: 101,
      teamId: 202,
      createdAt: "2023-01-01T00:00:00Z",
      createdBy: "admin",
      updatedAt: "2023-01-02T00:00:00Z",
      updatedBy: "admin",
    })
  }),
  openApiHttp.post("/api/admin/teams/{teamId}/mini-apps", async ({ request, response }) => {
    const body = await request.json()
    return response(201).json({
      id: Math.floor(Math.random() * 1000) + 10,
      ...body,
      miniAppStatus: "active",
      teamId: 202,
      createdAt: new Date().toISOString(),
      createdBy: "admin",
    })
  }),
  openApiHttp.patch("/api/admin/teams/{teamId}/mini-apps/{miniAppId}", async ({ params, request, response }) => {
    const id = parseInt(params.miniAppId as string)
    const body = await request.json()
    return response(200).json({
      id: id,
      miniAppStatus: "active",
      teamId: 202,
      updatedAt: new Date().toISOString(),
      updatedBy: "admin",
      createdAt: "",
      createdBy: "",
      name: `MiniApp ${id}`,
      description: `This is mini app ${id} for testing purposes.`,
      customerServiceContactNumber: "123-456-7890",
      customerServiceContactEmail: "<EMAIL>",
      ...body,
    })
  }),
  openApiHttp.delete("/api/admin/teams/{teamId}/mini-apps/{miniAppId}", ({ response }) => {
    return response.untyped(new Response())
  }),
  openApiHttp.get("/api/admin/teams/{teamId}/mini-apps/{miniAppId}/mini-app-versions", ({ response }) => {
    return response(200).json({
      items: [
        {
          id: 1,
          miniAppType: "builder",
          version: "1.0.1",
          releaseNote: "This release include fix for last issue",
          miniAppVersionStatus: "draft",
          thumbnailUrl: "",
          createdAt: new Date().toISOString(),
          createdBy: "Bani",
        },
        {
          id: 2,
          version: "1.0.0",
          miniAppType: "url",
          releaseNote: "Initial release",
          miniAppVersionStatus: "approved",
          thumbnailUrl: "",
          createdAt: new Date().toISOString(),
          createdBy: "Foo",
        },
      ],
      totalCount: 1,
      page: 1,
      pageSize: 10,
      totalPages: 1,
    })
  }),
]
