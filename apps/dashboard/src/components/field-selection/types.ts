export type LiquidFilters = {
  label: string
  value: string
  hasParam?: boolean
  description?: string
  example?: string
  sampleValue?: string
  params?: {
    placeholder?: string
    tip?: string
    description?: string
    type?: "string" | "number" | "variable"
    defaultValue?: string
    label: string
    required?: boolean
  }[]
}

export type LiquidFilterWithParam = {
  value: string
  params?: string[]
}
