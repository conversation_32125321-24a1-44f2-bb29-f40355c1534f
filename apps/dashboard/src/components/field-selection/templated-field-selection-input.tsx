import { Divider, IconButton, Input, Popover, Select, Text, Tooltip } from "@saf/ui"
import { IconAdjustmentsHorizontal, IconInfoCircle } from "@tabler/icons-react"
import * as React from "react"
import { useEffect, useState } from "react"
import { getFilters } from "./constants"
import { FieldSelectionInput } from "./field-selection-input"
import { LiquidFilterWithParam } from "./types"

type TemplatedFieldSelectionInputProps = React.ComponentPropsWithoutRef<typeof FieldSelectionInput>

export const TemplatedFieldSelectionInput = React.forwardRef<
  React.ElementRef<typeof FieldSelectionInput>,
  TemplatedFieldSelectionInputProps
>((props, ref) => {
  const [isOpen, setIsOpen] = useState(false)
  const [defaultValue, setDefaultValue] = useState("")
  const [fieldPath, setFieldPath] = useState("")
  const [selectedFilter, setSelectedFilter] = useState<LiquidFilterWithParam | null>(null)
  const [filterParams, setFilterParams] = useState<string[]>([])
  const filters = getFilters()

  // Parse incoming value to extract field path, filters, and default value
  useEffect(() => {
    if (!props.value) {
      setFieldPath("")
      setDefaultValue("")
      setSelectedFilter(null)
      setFilterParams([])
      return
    }

    // Extract content between {{ and }}
    const templateMatch = props.value.match(/{{(.*?)}}/)
    if (!templateMatch) {
      setFieldPath(props.value)
      return
    }

    const templateContent = templateMatch[1].trim()

    // Split by pipe to get all filters
    const parts = templateContent.split("|").map((part) => part.trim())

    // First part is the field path
    const path = parts[0]
    setFieldPath(path)

    // Check for filters
    if (parts.length > 1) {
      // Check for default filter (always last if present)
      const lastPart = parts[parts.length - 1]
      const defaultMatch = lastPart.match(/default:\s*["']?(.*?)["']?$/)

      if (defaultMatch) {
        setDefaultValue(defaultMatch[1].trim())
        // Remove default filter from parts
        parts.pop()
      } else {
        setDefaultValue("")
      }

      // Check for other filters
      if (parts.length > 1) {
        const filterPart = parts[1]
        const filterMatch = filterPart.match(/(\w+)(?::\s*(.*))?/)

        if (filterMatch) {
          const filterName = filterMatch[1]
          const filterParamsStr = filterMatch[2]

          const filter = filters.find((f) => f.value === filterName)
          if (filter) {
            setSelectedFilter({ value: filterName, params: [] })

            // Parse parameters if they exist
            if (filterParamsStr) {
              const params = filterParamsStr
                .split(",")
                .map((p) => p.trim())
                .map((p) => p.replace(/^["']|["']$/g, "")) // Remove quotes

              setFilterParams(params)
            } else {
              setFilterParams([])
            }
          }
        }
      } else {
        setSelectedFilter(null)
        setFilterParams([])
      }
    } else {
      setSelectedFilter(null)
      setFilterParams([])
      setDefaultValue("")
    }
  }, [props.value, filters])

  // Format the output value with filters and optional default value
  const formatOutput = (path: string, filter: LiquidFilterWithParam | null, params: string[], defaultVal: string) => {
    if (!path) return ""

    let formattedValue = path

    // Add filter if selected
    if (filter) {
      if (params.length > 0) {
        const formattedParams = params
          .map((p) => {
            // Add quotes for string parameters
            return isNaN(Number(p)) ? `"${p}"` : p
          })
          .join(", ")
        formattedValue += ` | ${filter.value}: ${formattedParams}`
      } else {
        formattedValue += ` | ${filter.value}`
      }
    }

    // Add default filter if provided
    if (defaultVal) {
      formattedValue += ` | default: "${defaultVal}"`
    }

    return `{{${formattedValue}}}`
  }

  // Handle field path change
  const handleValueChange = (path: string) => {
    if (!path) {
      props.onChange?.("")
      return
    }

    const output = formatOutput(path, selectedFilter, filterParams, defaultValue)
    props.onChange?.(output)
  }

  // Handle filter selection
  const handleFilterChange = (filterValue: string) => {
    const filter = filters.find((f) => f.value === filterValue)

    if (filter) {
      setSelectedFilter({ value: filterValue, params: [] })
      // Initialize params array with empty strings based on filter definition
      setFilterParams(filter.params ? Array(filter.params.length).fill("") : [])
    } else {
      setSelectedFilter(null)
      setFilterParams([])
    }

    // Update output
    const output = formatOutput(
      fieldPath,
      filter ? { value: filterValue, params: [] } : null,
      filter?.params ? Array(filter.params.length).fill("") : [],
      defaultValue,
    )
    props.onChange?.(output)
  }

  // Handle parameter change
  const handleParamChange = (index: number, value: string) => {
    const newParams = [...filterParams]
    newParams[index] = value
    setFilterParams(newParams)

    // Update output
    const output = formatOutput(fieldPath, selectedFilter, newParams, defaultValue)
    props.onChange?.(output)
  }

  // Handle default value change
  const handleDefaultValueChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newDefaultValue = e.target.value
    setDefaultValue(newDefaultValue)

    if (fieldPath) {
      const output = formatOutput(fieldPath, selectedFilter, filterParams, newDefaultValue)
      props.onChange?.(output)
    }
  }

  // Get the selected filter object
  const selectedFilterObj = selectedFilter ? filters.find((f) => f.value === selectedFilter.value) : null

  return (
    <div className="relative">
      <div className="flex items-start gap-2">
        <div className="flex-1">
          <FieldSelectionInput
            {...props}
            ref={ref}
            value={`{{${fieldPath}}}`}
            onChange={(value) => {
              // Extract path from the template
              const match = value?.match(/{{(.*?)}}/)
              const path = match ? match[1].trim() : ""
              setFieldPath(path)
              handleValueChange(path)
            }}
            isTemplated={true}
          />
        </div>
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <Popover.Trigger asChild>
            <IconButton variant="secondary" size="base" onClick={() => setIsOpen(true)}>
              <IconAdjustmentsHorizontal size={16} className="text-ui-fg-subtle" />
            </IconButton>
          </Popover.Trigger>
          <Popover.Content className="w-[320px] px-0 py-2">
            <div className="space-y-2">
              <div className="px-2">
                <Input
                  size="small"
                  value={defaultValue}
                  onChange={handleDefaultValueChange}
                  placeholder="Enter default value..."
                />
              </div>
              <Divider />
              <div className="space-y-2 px-2">
                <Text size="xsmall" weight="plus" className="text-ui-fg-subtle">
                  Transform
                </Text>
                <Select value={selectedFilter?.value} key={selectedFilter?.value} onValueChange={handleFilterChange}>
                  <Select.Trigger>
                    <Select.Value placeholder="Select a filter..." />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="none">None</Select.Item>
                    {filters.map((filter) => (
                      <Select.Item key={filter.value} value={filter.value}>
                        {filter.label}
                      </Select.Item>
                    ))}
                  </Select.Content>
                </Select>
                <div className="flex items-center gap-1">
                  {selectedFilterObj?.description && (
                    <Text size="small" className="text-ui-fg-subtle">
                      {selectedFilterObj.description}
                    </Text>
                  )}
                  {selectedFilterObj?.example && (
                    <Tooltip
                      maxWidth={500}
                      content={
                        <Text size="small" className="font-mono">
                          {selectedFilterObj.example}
                        </Text>
                      }
                    >
                      <IconInfoCircle size={14} className="text-ui-fg-subtle" />
                    </Tooltip>
                  )}
                </div>
              </div>

              {selectedFilterObj?.hasParam && selectedFilterObj.params && (
                <div className="space-y-3 px-2">
                  {selectedFilterObj.params.map((param, index) => (
                    <div key={index} className="space-y-1">
                      <Text size="xsmall" weight="plus" className="text-ui-fg-subtle">
                        {param.label}
                      </Text>
                      <Input
                        size="small"
                        type={param.type === "number" ? "number" : "text"}
                        placeholder={param.placeholder || param.label}
                        value={filterParams[index] || ""}
                        onChange={(e) => handleParamChange(index, e.target.value)}
                      />
                      {param.description && (
                        <Text size="small" className="text-ui-fg-muted">
                          {param.description}
                        </Text>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </Popover.Content>
        </Popover>
      </div>
    </div>
  )
})

TemplatedFieldSelectionInput.displayName = "TemplatedFieldSelectionInput"
