:root {
  --tt-button-default-icon-color: var(--tt-gray-light-a-600);

  --tiptap-image-upload-active: var(--tt-brand-color-500);
  --tiptap-image-upload-progress-bg: var(--tt-brand-color-50);
  --tiptap-image-upload-icon-bg: var(--tt-brand-color-500);

  --tiptap-image-upload-text-color: var(--tt-gray-light-a-700);
  --tiptap-image-upload-subtext-color: var(--tt-gray-light-a-400);
  --tiptap-image-upload-border: var(--tt-gray-light-a-300);
  --tiptap-image-upload-border-hover: var(--tt-gray-light-a-400);
  --tiptap-image-upload-border-active: var(--tt-brand-color-500);

  --tiptap-image-upload-icon-doc-bg: var(--tt-gray-light-a-200);
  --tiptap-image-upload-icon-doc-border: var(--tt-gray-light-300);
  --tiptap-image-upload-icon-color: var(--white);
}

.dark {
  --tt-button-default-icon-color: var(--tt-gray-dark-a-600);

  --tiptap-image-upload-active: var(--tt-brand-color-400);
  --tiptap-image-upload-progress-bg: var(--tt-brand-color-900);
  --tiptap-image-upload-icon-bg: var(--tt-brand-color-400);

  --tiptap-image-upload-text-color: var(--tt-gray-dark-a-700);
  --tiptap-image-upload-subtext-color: var(--tt-gray-dark-a-400);
  --tiptap-image-upload-border: var(--tt-gray-dark-a-300);
  --tiptap-image-upload-border-hover: var(--tt-gray-dark-a-400);
  --tiptap-image-upload-border-active: var(--tt-brand-color-400);

  --tiptap-image-upload-icon-doc-bg: var(--tt-gray-dark-a-200);
  --tiptap-image-upload-icon-doc-border: var(--tt-gray-dark-300);
  --tiptap-image-upload-icon-color: var(--black);
}

.tiptap-image-upload {
  margin: 2rem 0;

  input[type="file"] {
    display: none;
  }

  .tiptap-image-upload-dropzone {
    position: relative;
    width: 3.125rem;
    height: 3.75rem;
    display: inline-flex;
    align-items: flex-start;
    justify-content: center;
    -webkit-user-select: none; /* Safari */
    -ms-user-select: none; /* IE 10 and IE 11 */
    user-select: none;
  }

  .tiptap-image-upload-icon-container {
    position: absolute;
    width: 1.75rem;
    height: 1.75rem;
    bottom: 0;
    right: 0;
    background-color: var(--tiptap-image-upload-icon-bg);
    border-radius: var(--tt-radius-lg, 0.75rem);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .tiptap-image-upload-icon {
    width: 0.875rem;
    height: 0.875rem;
    color: var(--tiptap-image-upload-icon-color);
  }

  .tiptap-image-upload-dropzone-rect-primary {
    color: var(--tiptap-image-upload-icon-doc-bg);
    position: absolute;
  }

  .tiptap-image-upload-dropzone-rect-secondary {
    position: absolute;
    top: 0;
    right: 0.25rem;
    bottom: 0;
    color: var(--tiptap-image-upload-icon-doc-border);
  }

  .tiptap-image-upload-text {
    color: var(--tiptap-image-upload-text-color);
    font-weight: 500;
    font-size: 0.875rem;
    line-height: normal;

    em {
      font-style: normal;
      text-decoration: underline;
    }
  }

  .tiptap-image-upload-subtext {
    color: var(--tiptap-image-upload-subtext-color);
    font-weight: 600;
    line-height: normal;
    font-size: 0.75rem;
  }

  .tiptap-image-upload-preview {
    position: relative;
    border-radius: var(--tt-radius-md, 0.5rem);
    overflow: hidden;

    .tiptap-image-upload-progress {
      position: absolute;
      inset: 0;
      background-color: var(--tiptap-image-upload-progress-bg);
      transition: all 300ms ease-out;
    }

    .tiptap-image-upload-preview-content {
      position: relative;
      border: 1px solid var(--tiptap-image-upload-border);
      border-radius: var(--tt-radius-md, 0.5rem);
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .tiptap-image-upload-file-info {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      height: 2rem;

      .tiptap-image-upload-file-icon {
        padding: 0.5rem;
        background-color: var(--tiptap-image-upload-icon-bg);
        border-radius: var(--tt-radius-lg, 0.75rem);

        svg {
          width: 0.875rem;
          height: 0.875rem;
          color: var(--tiptap-image-upload-icon-color);
        }
      }
    }

    .tiptap-image-upload-details {
      display: flex;
      flex-direction: column;
    }

    .tiptap-image-upload-actions {
      display: flex;
      align-items: center;

      .tiptap-image-upload-progress-text {
        font-size: 0.75rem;
        color: var(--tiptap-image-upload-border-active);
      }

      .tiptap-image-upload-close-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        color: var(--tt-button-default-icon-color);
        transition: color 200ms ease;

        svg {
          width: 1rem;
          height: 1rem;
        }
      }
    }
  }

  .tiptap-image-upload-dragger {
    padding: 2rem 1.5rem;
    border: 1.5px dashed var(--tiptap-image-upload-border);
    border-radius: var(--tt-radius-md, 0.5rem);
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &-active {
      border-color: var(--tiptap-image-upload-border-active);
      background-color: rgba(
        var(--tiptap-image-upload-active-rgb, 0, 0, 255),
        0.05
      );
    }
  }

  .tiptap-image-upload-content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 0.25rem;
    -webkit-user-select: none; /* Safari */
    -ms-user-select: none; /* IE 10 and IE 11 */
    user-select: none;
  }
}

.tiptap.ProseMirror.ProseMirror-focused {
  .ProseMirror-selectednode .tiptap-image-upload-dragger {
    border-color: var(--tiptap-image-upload-active);
  }
}
