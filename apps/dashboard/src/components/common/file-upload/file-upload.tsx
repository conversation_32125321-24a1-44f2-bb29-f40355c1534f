import { ArrowDownTray, XCircle } from "@medusajs/icons"
import { IconButton, Text, clx } from "@saf/ui"
import { ChangeEvent, DragEvent, useRef, useState } from "react"

export interface FileType {
  id: string
  url: string
  file: File
}

export interface FileUploadProps {
  label: string
  value?: File
  multiple?: boolean
  hint?: string
  hasError?: boolean
  formats: string[]
  onUploaded: (files: FileType[]) => void
  onRemove: () => void
}

export const FileUpload = ({
  label,
  value,
  hint,
  multiple = true,
  hasError,
  formats,
  onUploaded,
  onRemove,
}: FileUploadProps) => {
  const [isDragOver, setIsDragOver] = useState<boolean>(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const dropZoneRef = useRef<HTMLButtonElement>(null)

  const handleOpenFileSelector = () => {
    inputRef.current?.click()
  }

  const handleDragEnter = (event: DragEvent) => {
    event.preventDefault()
    event.stopPropagation()

    const files = event.dataTransfer?.files
    if (!files) {
      return
    }

    setIsDragOver(true)
  }

  const handleDragLeave = (event: DragEvent) => {
    event.preventDefault()
    event.stopPropagation()

    if (!dropZoneRef.current || dropZoneRef.current.contains(event.relatedTarget as Node)) {
      return
    }

    setIsDragOver(false)
  }

  const handleUploaded = (files: FileList | null) => {
    if (!files) {
      return
    }

    const fileList = Array.from(files)
    const fileObj = fileList.map((file) => {
      const id = Math.random().toString(36).substring(7)

      const previewUrl = URL.createObjectURL(file)
      return {
        id: id,
        url: previewUrl,
        file,
      }
    })

    onUploaded(fileObj)
  }

  const handleDrop = (event: DragEvent) => {
    event.preventDefault()
    event.stopPropagation()

    setIsDragOver(false)

    handleUploaded(event.dataTransfer?.files)
  }

  const handleFileChange = async (event: ChangeEvent<HTMLInputElement>) => {
    handleUploaded(event.target.files)
  }

  return value ? (
    <div className="relative">
      <img src={URL.createObjectURL(value)} className="w-full" />
      <IconButton className="absolute right-4 top-2" onClick={onRemove}>
        <XCircle />
      </IconButton>
    </div>
  ) : (
    <div>
      <button
        ref={dropZoneRef}
        type="button"
        onClick={handleOpenFileSelector}
        onDrop={handleDrop}
        onDragOver={(e) => e.preventDefault()}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        className={clx(
          "group flex w-full flex-col items-center gap-y-2 rounded-lg border border-dashed border-ui-border-strong bg-ui-bg-component p-8 transition-fg",
          "hover:border-ui-border-interactive focus:border-ui-border-interactive",
          "outline-none focus:border-solid focus:shadow-borders-focus",
          {
            "!border-ui-border-error": hasError,
            "!border-ui-border-interactive": isDragOver,
          },
        )}
      >
        <div className="flex items-center gap-x-2 text-ui-fg-subtle group-disabled:text-ui-fg-disabled">
          <ArrowDownTray />
          <Text>{label}</Text>
        </div>
        {!!hint && (
          <Text size="small" leading="compact" className="text-ui-fg-muted group-disabled:text-ui-fg-disabled">
            {hint}
          </Text>
        )}
      </button>
      <input
        hidden
        ref={inputRef}
        onChange={handleFileChange}
        type="file"
        accept={formats.join(",")}
        multiple={multiple}
      />
    </div>
  )
}
