import { RichText } from "@/components/inputs/rich-text"
import { Dialog, DialogDescription } from "@ariakit/react"
import { XCircle } from "@medusajs/icons"
import { IconButton, Text, clx } from "@saf/ui"
import { ReactNode, useState } from "react"

export type SectionRowProps = {
  title: string
  value?: ReactNode | string | null
  type?: string
  actions?: ReactNode
}

export const SectionRow = ({ title, value, actions, type }: SectionRowProps) => {
  const [isImageModelOpen, setIsImageModalOpen] = useState(false)
  const isValueString = typeof value === "string" || !value

  return (
    <div
      className={clx(`grid w-full grid-cols-2 items-center gap-4 px-6 py-4 text-ui-fg-subtle`, {
        "grid-cols-[1fr_1fr_28px]": !!actions,
      })}
    >
      <Text size="small" weight="plus" leading="compact">
        {title}
      </Text>

      {isValueString ? (
        type == "image" ? (
          <>
            <img
              src={(value ?? "").toString()}
              className="w-20 cursor-pointer"
              onClick={() => setIsImageModalOpen(true)}
            />
            <Dialog
              className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 rounded-md bg-ui-bg-subtle p-8 shadow-md"
              open={isImageModelOpen}
              onClose={() => setIsImageModalOpen(false)}
            >
              <DialogDescription>
                <IconButton className="absolute right-4 top-2" onClick={() => setIsImageModalOpen(false)}>
                  <XCircle />
                </IconButton>
                <img src={(value ?? "").toString()} className="mt-8 max-h-[80vh] max-w-full object-contain" />
              </DialogDescription>
            </Dialog>
          </>
        ) : type == "richText" ? (
          <RichText strContent={value?.toString()} isPreview={true} />
        ) : (
          <Text size="small" leading="compact" className="whitespace-pre-line text-pretty">
            {value ?? "-"}
          </Text>
        )
      ) : (
        <div className="flex flex-wrap gap-1">{value}</div>
      )}

      {actions && <div>{actions}</div>}
    </div>
  )
}
