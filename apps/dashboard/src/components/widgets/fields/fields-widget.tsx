import { cn } from "@/lib/utils"
import { components } from "@saf/sdk"
import { cva, type VariantProps } from "class-variance-authority"

const fieldsVariants = cva("bg-ui-bg-base border-ui-border-base px-4 py-0.5 divide-y", {
  variants: {
    style: {
      default: "[&>div]:py-4",
      compact: "[&>div]:py-3 text-sm",
    },
  },
  defaultVariants: {
    style: "default",
  },
})

export interface FieldsWidgetProps extends VariantProps<typeof fieldsVariants> {
  content: string
}

export const FieldsWidget = ({ data }: { data: components["schemas"]["FieldsWidget"] }) => {
  const fields = data.config.fields || []

  return (
    <div
      className={cn(
        fieldsVariants({
          style: data.config.design?.style,
        }),
      )}
    >
      {fields.length >= 0 &&
        fields.map((field, index) => (
          <div className="grid grid-cols-2 justify-between gap-2" key={`${field.label}-${field.value}-${index}`}>
            <div className="text-ui-fg-subtle">{field.label}</div>
            <div className="text-end font-medium">{field.value}</div>
          </div>
        ))}
    </div>
  )
}
