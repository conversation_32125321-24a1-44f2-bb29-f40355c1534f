import { Form } from "@/components/common/form"
import { Options } from "@/configs"
import { createEnumFromOptions } from "@/lib/conversion"
import { zodResolver } from "@hookform/resolvers/zod"
import { components } from "@saf/sdk"
import { Button, IconButton, Input, Select, Text } from "@saf/ui"
import { Trash } from "@medusajs/icons"
import { useEffect, useState } from "react"
import { useFieldArray, useForm } from "react-hook-form"
import { useDeepCompareEffect } from "use-deep-compare"
import { z } from "zod"
import { generalWidgetEditorSchema, GeneralWidgetFields } from "../general"
import { Field } from "@/components/common/field"

const styleOption: Options = [
  {
    label: "Default",
    value: "default",
  },
  {
    label: "Compact",
    value: "compact",
  },
]

const fieldsEditorSchema = generalWidgetEditorSchema.merge(
  z.object({
    fields: z.array(
      z.object({
        label: z.string(),
        value: z.string(),
      }),
    ),
    style: z.enum(createEnumFromOptions(styleOption)),
  }),
)

type FieldsEditorSchema = z.infer<typeof fieldsEditorSchema>

export const FieldsEditor = ({
  data,
  onUpdate,
}: {
  data: components["schemas"]["FieldsWidget"]
  onUpdate: (updatedData: components["schemas"]["FieldsWidget"]) => void
}) => {
  const [initialRender, setInitialRender] = useState(true)
  const form = useForm<FieldsEditorSchema>({
    resolver: zodResolver(fieldsEditorSchema),
    defaultValues: {
      name: "",
      isHidden: false,
      fields: [],
      style: "default",
    },
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "fields",
  })

  useDeepCompareEffect(() => {
    if (data && initialRender) {
      form.reset({
        name: data.name || "",
        isHidden: data.isHidden ?? false,
        fields: data.config.fields || [],
        style: data.config.design?.style || "default",
      })
      setInitialRender(false)
    }
  }, [data, form, initialRender])

  useEffect(() => {
    if (!data) return

    return form.subscribe({
      name: "",
      formState: {
        values: true,
      },
      callback({ values }) {
        const updatedData: components["schemas"]["FieldsWidget"] = {
          ...data,
          name: values.name || "",
          isHidden: values.isHidden ?? false,
          config: {
            ...data.config,
            fields: values.fields || "",
            design: {
              style: values.style as components["schemas"]["FieldsWidget"]["config"]["design"]["style"],
            },
          },
        }
        onUpdate(updatedData)
      },
    })
  }, [data, form, onUpdate])

  return (
    <Form {...form}>
      <form className="divide-y">
        <GeneralWidgetFields />
        <div className="flex flex-col gap-4 p-4">
          <div className="flex items-center justify-between">
            <Text className="uppercase" size="xsmall" weight="plus">
              Content
            </Text>
            <Button type="button" variant="secondary" size="small" onClick={() => append({ label: "", value: "" })}>
              + Add Item
            </Button>
          </div>

          {fields.map((field, index) => (
            <div key={field.id} className="flex flex-row items-center space-x-2">
              <Field control={form.control} name={`fields.${index}.label`}>
                <Input placeholder="Label" />
              </Field>
              <Field control={form.control} name={`fields.${index}.value`}>
                <Input placeholder="Value" />
              </Field>
              <IconButton type="button" variant="transparent" onClick={() => remove(index)}>
                <Trash className="h-4 w-4" />
              </IconButton>
            </div>
          ))}
        </div>
        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Design
          </Text>
          <div className="space-y-4">
            <Form.Field
              control={form.control}
              name="style"
              render={({ field: { ref, onChange, ...field } }) => {
                return (
                  <Form.Item className="flex-row justify-between gap-4">
                    <Form.Label>Style</Form.Label>
                    <Form.Control>
                      <Select {...field} onValueChange={onChange}>
                        <Select.Trigger ref={ref} className="w-[170px]">
                          <Select.Value />
                        </Select.Trigger>
                        <Select.Content>
                          {styleOption.map((item) => (
                            <Select.Item key={item.value} value={item.value}>
                              {item.label}
                            </Select.Item>
                          ))}
                        </Select.Content>
                      </Select>
                    </Form.Control>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />
          </div>
        </div>
      </form>
    </Form>
  )
}
