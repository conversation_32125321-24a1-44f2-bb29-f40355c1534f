// Demo file to showcase liquid template functionality in CollectionWidget
import { components } from "@saf/sdk"
import { CollectionWidget } from "./collection-widget"

// Example configurations showing different liquid template patterns
const demoConfigurations: Array<{
  name: string
  config: components["schemas"]["CollectionWidget"]["config"]
}> = [
  {
    name: "Basic Templates",
    config: {
      bindingConfig: { dataSourceId: 1 },
      style: "list" as const,
      itemsData: {
        title: "{{title}}",
        subtitle: "{{subtitle}}",
        meta: "{{meta}}",
        image: "{{image}}",
      },
      design: {
        size: "default" as const,
        style: "default" as const,
        imageShape: "square" as const,
      },
      options: { limitItems: 3 },
      pagination: {
        enabled: false,
        requestPage: "",
        requestLimit: "",
      },
    },
  },
  {
    name: "Advanced Templates with Filters",
    config: {
      bindingConfig: { dataSourceId: 2 },
      style: "grid" as const,
      itemsData: {
        title: "{{title | upcase}}",
        subtitle: "{{subtitle | truncate: 30}}",
        meta: "{{status | capitalize}} - ${{price}} {{currency}}",
        image: "{{image}}",
      },
      design: {
        size: "compact" as const,
        style: "card" as const,
        imageShape: "circle" as const,
      },
      options: { limitItems: 4 },
      pagination: {
        enabled: false,
        requestPage: "",
        requestLimit: "",
      },
    },
  },
  {
    name: "Complex Templates with Nested Data",
    config: {
      bindingConfig: { dataSourceId: 3 },
      style: "list" as const,
      itemsData: {
        title: "{{title}} by {{author.name}}",
        subtitle: "{{subtitle | truncate: 50}}...",
        meta: "Created: {{createdAt | date: '%B %d, %Y'}}",
        image: "{{image}}",
      },
      design: {
        size: "default" as const,
        style: "default" as const,
        imageShape: "square" as const,
      },
      options: { limitItems: 2 },
      pagination: {
        enabled: true,
        requestPage: "page",
        requestLimit: "limit",
        size: 10,
      },
    },
  },
]

export const CollectionWidgetPocDemo = () => {
  return (
    <div className="space-y-8 p-2">
      <h1 className="text-2xl font-bold">Collection Widget Liquid Template Demo</h1>
      <p className="text-gray-600">
        This demo shows how the CollectionWidget uses liquid templates to render dynamic content. Each configuration
        below demonstrates different liquid template patterns.
      </p>

      {demoConfigurations.map((demo, index) => (
        <div key={index} className="rounded-lg border p-4">
          <h2 className="mb-2 text-lg font-semibold">{demo.name}</h2>

          {/* Show the template configuration */}
          <div className="mb-4 rounded bg-gray-100 p-3">
            <h3 className="mb-2 font-medium">Template Configuration:</h3>
            <pre className="overflow-x-auto text-sm">{JSON.stringify(demo.config.itemsData, null, 2)}</pre>
          </div>

          {/* Render the widget */}
          <div className="border-t pt-4">
            <h3 className="mb-2 font-medium">Rendered Output:</h3>
            <CollectionWidget
              data={{
                id: index + 1,
                name: `Demo Widget ${index + 1}`,
                position: index + 1,
                isHidden: false,
                widgetType: "collection" as const,
                config: demo.config,
              }}
            />
          </div>
        </div>
      ))}

      <div className="rounded-lg bg-blue-50 p-4">
        <h3 className="mb-2 font-semibold text-blue-800">Liquid Template Features Demonstrated:</h3>
        <ul className="list-inside list-disc space-y-1 text-blue-700">
          <li>
            <strong>Basic interpolation:</strong> <code>{"{{title}}"}</code> - Direct field access
          </li>
          <li>
            <strong>Filters:</strong> <code>{"{{title | upcase}}"}</code> - Transform values
          </li>
          <li>
            <strong>Truncation:</strong> <code>{"{{subtitle | truncate: 30}}"}</code> - Limit text length
          </li>
          <li>
            <strong>Nested objects:</strong> <code>{"{{author.name}}"}</code> - Access nested properties
          </li>
          <li>
            <strong>Date formatting:</strong> <code>{"{{createdAt | date: '%B %d, %Y'}}"}</code> - Format dates
          </li>
          <li>
            <strong>String combination:</strong> Multiple fields and text in one template
          </li>
        </ul>
      </div>
    </div>
  )
}
