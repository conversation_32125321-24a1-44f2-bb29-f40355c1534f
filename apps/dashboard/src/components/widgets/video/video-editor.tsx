import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { Options } from "@/configs"
import { createEnumFromOptions } from "@/lib/conversion"
import { zodResolver } from "@hookform/resolvers/zod"
import { components } from "@saf/sdk"
import { Checkbox, Input, Select, Text } from "@saf/ui"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { useDeepCompareEffect } from "use-deep-compare"
import { z } from "zod"
import { generalWidgetEditorSchema, GeneralWidgetFields } from "../general"

const aspectRatioOptions: Options = [
  { label: "1:1", value: "1:1" },
  { label: "16:9", value: "16:9" },
  { label: "4:3", value: "4:3" },
  { label: "3:2", value: "3:2" },
]

const videoEditorSchema = generalWidgetEditorSchema.merge(
  z.object({
    url: z
      .string()
      .min(1, "Video is required")
      .refine(
        (value) => {
          if (!value) return true
          try {
            new URL(value)
            return true
          } catch {
            return false
          }
        },
        { message: "Video URL is invalid" },
      ),
    aspectRatio: z.enum(createEnumFromOptions(aspectRatioOptions)).optional(),
    fullWidth: z.boolean().optional(),
  }),
)

type VideoEditorSchema = z.infer<typeof videoEditorSchema>

export const VideoEditor = ({
  data,
  onUpdate,
}: {
  data: components["schemas"]["VideoWidget"]
  onUpdate: (updatedData: components["schemas"]["VideoWidget"]) => void
}) => {
  const [initialRender, setInitialRender] = useState(true)
  const form = useForm<VideoEditorSchema>({
    resolver: zodResolver(videoEditorSchema),
    defaultValues: {
      name: "",
      isHidden: false,
      url: "",
      aspectRatio: "1:1",
      fullWidth: false,
    },
    mode: "onChange",
  })

  useDeepCompareEffect(() => {
    if (data && initialRender) {
      const url = data.config.url || ""
      form.reset({
        name: data.name || "",
        isHidden: data.isHidden ?? false,
        url: url,
        aspectRatio: data.config.design?.aspectRatio || "1:1",
        fullWidth: data.config.design?.fullWidth ?? false,
      })
      setInitialRender(false)
    }
  }, [data, form, initialRender])

  useEffect(() => {
    if (!data) return

    return form.subscribe({
      name: "",
      formState: {
        values: true,
      },
      callback({ values }) {
        const updatedData: components["schemas"]["VideoWidget"] = {
          ...data,
          name: values.name || "",
          isHidden: values.isHidden ?? false,
          config: {
            ...data.config,
            url: values.url || "",
            design: {
              aspectRatio: values.aspectRatio as "1:1" | "16:9" | "4:3" | "3:2",
              fullWidth: values.fullWidth ?? false,
            },
          },
        }

        onUpdate(updatedData)
      },
    })
  }, [data, form, onUpdate])

  return (
    <Form {...form}>
      <form className="divide-y">
        <GeneralWidgetFields />
        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Content
          </Text>

          <Field control={form.control} name="url" label="Video URL">
            <Input placeholder="http://example.com/video.mp4" />
          </Field>
        </div>

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Design
          </Text>
          <div className="space-y-4">
            <Form.Field
              control={form.control}
              name="aspectRatio"
              render={({ field: { ref, onChange, ...field } }) => {
                return (
                  <Form.Item className="flex-row justify-between gap-4">
                    <Form.Label>Aspect Ratio</Form.Label>
                    <Form.Control>
                      <Select {...field} onValueChange={onChange}>
                        <Select.Trigger ref={ref} className="w-[170px]">
                          <Select.Value />
                        </Select.Trigger>
                        <Select.Content>
                          {aspectRatioOptions.map((item) => (
                            <Select.Item key={item.value} value={item.value}>
                              {item.label}
                            </Select.Item>
                          ))}
                        </Select.Content>
                      </Select>
                    </Form.Control>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />

            <Form.Field
              control={form.control}
              name="fullWidth"
              render={({ field: { value, onChange, ...field } }) => {
                return (
                  <Form.Item className="flex flex-row items-center gap-3">
                    <Form.Control>
                      <Checkbox checked={value} onCheckedChange={onChange} {...field} />
                    </Form.Control>
                    <Form.Label>Full Width</Form.Label>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />
          </div>
        </div>
      </form>
    </Form>
  )
}
