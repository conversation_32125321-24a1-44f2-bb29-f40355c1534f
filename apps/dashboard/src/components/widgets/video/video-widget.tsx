import { cn } from "@/lib/utils"
import { components } from "@saf/sdk"
import { IconVideo } from "@tabler/icons-react"
import { cva, type VariantProps } from "class-variance-authority"
import { memo } from "react"

const videoVariants = cva("overflow-hidden", {
  variants: {
    aspectRatio: {
      "1:1": "aspect-square",
      "16:9": "aspect-video",
      "4:3": "aspect-[4/3]",
      "3:2": "aspect-[3/2]",
    },
    fullWidth: {
      true: "w-full",
      false: "max-w-md mx-auto",
    },
  },
  defaultVariants: {
    aspectRatio: "1:1",
    fullWidth: false,
  },
})

const videoContainerVariants = cva("", {
  variants: {
    fullWidth: {
      true: "w-full",
      false: "w-fit mx-auto",
    },
  },
  defaultVariants: {
    fullWidth: false,
  },
})

export interface VideoWidgetProps extends VariantProps<typeof videoVariants> {
  content: string
  alt?: string
}

export const VideoWidget = memo(({ data }: { data: components["schemas"]["VideoWidget"] }) => {
  const { url, design } = data.config || {}

  if (!url) {
    return (
      <div className="px-4 py-2">
        <div className="flex items-center justify-center border border-dashed border-ui-border-base bg-ui-bg-subtle p-8 text-center">
          <div className="text-ui-text-muted">
            <IconVideo className="mx-auto" />
            <p className="mt-2 text-sm">No video selected</p>
          </div>
        </div>
      </div>
    )
  }

  // Safely process YouTube URLs to ensure proper embedding with enhanced player options
  const getYouTubeEmbedUrl = (url: string): string => {
    try {
      const videoUrl = new URL(url)
      let videoId = ""

      // Handle youtube.com/watch?v=VIDEO_ID
      if (videoUrl.hostname.includes("youtube.com") && videoUrl.pathname.includes("/watch")) {
        videoId = videoUrl.searchParams.get("v") || ""
      }

      // Handle youtu.be/VIDEO_ID
      else if (videoUrl.hostname === "youtu.be") {
        videoId = videoUrl.pathname.substring(1)
      }

      // Handle youtube.com/embed/VIDEO_ID
      else if (videoUrl.hostname.includes("youtube.com") && videoUrl.pathname.includes("/embed/")) {
        videoId = videoUrl.pathname.split("/embed/")[1]
      }

      const parametersString = "rel=0&modestbranding=1&showinfo=0&controls=1&iv_load_policy=3&fs=1&playsinline=1"

      if (videoId) {
        // Build embed URL with enhanced player parameters:
        // - rel=0: Disable related videos at the end
        // - modestbranding=1: Reduce YouTube branding (removes YouTube logo)
        // - showinfo=0: Hide video title and uploader info
        // - controls=1: Show player controls
        // - iv_load_policy=3: Hide video annotations
        // - fs=1: Enable fullscreen button
        // - playsinline=1: Play inline on mobile devices
        return `https://www.youtube.com/embed/${videoId}?${parametersString}`
      }

      // If it's already an embed URL but without parameters, add our parameters
      if (videoUrl.pathname.includes("/embed/")) {
        // Preserve any existing parameters and add our enhanced ones
        const existingParams = videoUrl.search || "?"
        const hasParams = existingParams.length > 1
        const connector = hasParams ? "&" : ""
        return `${url}${existingParams}${connector}?${parametersString}`
      }

      // Default fallback - return original URL
      return url
    } catch {
      return url
    }
  }

  const isYouTubeUrl = url.includes("youtube.com") || url.includes("youtu.be")
  const embedUrl = isYouTubeUrl ? getYouTubeEmbedUrl(url) : url

  return (
    <div className={cn("py-0.5", !design?.fullWidth && "px-4")}>
      <div
        className={cn(
          videoContainerVariants({
            fullWidth: design?.fullWidth,
          }),
        )}
      >
        <div
          className={cn(
            videoVariants({
              aspectRatio: design?.aspectRatio,
              fullWidth: design?.fullWidth,
            }),
          )}
        >
          {isYouTubeUrl ? (
            <iframe
              src={embedUrl}
              className="h-full w-full"
              title={data.name || "YouTube video player"}
              // Allow only essential features for security
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowFullScreen
              loading="lazy"
              referrerPolicy="strict-origin-when-cross-origin"
            ></iframe>
          ) : (
            <video
              src={url}
              className="h-full w-full"
              controls
              preload="metadata"
              playsInline
              aria-label={data.name || "Video player"}
              onError={(e) => {
                const target = e.target as HTMLVideoElement
                target.style.display = "none"
                const parent = target.parentElement
                if (parent) {
                  parent.innerHTML = `
                  <div class="flex h-full w-full items-center justify-center text-ui-text-muted">
                    <div class="text-center">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="mx-auto h-12 w-12"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            aria-hidden="true"
                            >
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <path d="M15 10l4.553 -2.276a1 1 0 0 1 1.447 .894v6.764a1 1 0 0 1 -1.447 .894l-4.553 -2.276v-4z" />
                            <path d="M3 6m0 2a2 2 0 0 1 2 -2h8a2 2 0 0 1 2 2v8a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2z" />
                            <path d="M7 12l4 0" />
                            <path d="M9 10l0 4" />
                        </svg>
                        <p class="mt-2 text-sm" role="alert">Failed to load video</p>
                    </div>
                  </div>
                `
                }
              }}
            />
          )}
        </div>
      </div>
    </div>
  )
})

VideoWidget.displayName = "VideoWidget"
