import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { Options } from "@/configs"
import { createEnumFromOptions } from "@/lib/conversion"
import { zodResolver } from "@hookform/resolvers/zod"
import { components } from "@saf/sdk"
import { Hint, Input, Label, Select, Text } from "@saf/ui"
import { useEffect, useRef, useState } from "react"
import { useForm } from "react-hook-form"
import { useDeepCompareEffect } from "use-deep-compare"
import { z } from "zod"
import { generalWidgetEditorSchema, GeneralWidgetFields } from "../general"
import { IMAGE_SIZE_LIMIT_IN_MB } from "../image"

const styleOptions: Options = [
  { label: "Simple", value: "simple" },
  { label: "Banner", value: "banner" },
]

const imageFillOptions: Options = [
  { label: "Fill", value: "fill" },
  { label: "Contain", value: "contain" },
]

const titleEditorSchema = generalWidgetEditorSchema.merge(
  z.object({
    style: z.enum(createEnumFromOptions(styleOptions)),
    title: z.string().optional(),
    subtitle: z.string().optional(),
    meta: z.string().optional(),
    image: z.string().optional(),
    imageFill: z.enum(createEnumFromOptions(imageFillOptions)),
  }),
)

type TitleEditorSchema = z.infer<typeof titleEditorSchema>

export const TitleEditor = ({
  data,
  onUpdate,
}: {
  data: components["schemas"]["TitleWidget"]
  onUpdate: (updatedData: components["schemas"]["TitleWidget"]) => void
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [fileInputError, setFileInputError] = useState<string | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string>("")
  const [initialRender, setInitialRender] = useState(true)
  const form = useForm<TitleEditorSchema>({
    resolver: zodResolver(titleEditorSchema),
    defaultValues: {
      name: "",
      isHidden: false,
      style: "simple",
      title: "",
      subtitle: "",
      meta: "",
      image: "",
      imageFill: "contain",
    },
  })

  useDeepCompareEffect(() => {
    if (data && initialRender) {
      const imageUrl = data.config.data?.image || ""
      form.reset({
        name: data.name || "",
        isHidden: data.isHidden ?? false,
        style: data.config.style || "simple",
        title: data.config.data?.title || "",
        subtitle: data.config.data?.subtitle || "",
        meta: data.config.data?.meta || "",
        image: imageUrl,
        imageFill: data.config.design?.imageFill || "contain",
      })
      setPreviewUrl(imageUrl)
      setInitialRender(false)
    }
  }, [data, form, initialRender])

  useEffect(() => {
    if (!data) return

    return form.subscribe({
      name: "",
      formState: {
        values: true,
      },
      callback({ values }) {
        const updatedData: components["schemas"]["TitleWidget"] = {
          ...data,
          name: values.name || "",
          isHidden: values.isHidden ?? false,
          config: {
            ...data.config,
            style: values.style as components["schemas"]["TitleWidget"]["config"]["style"],
            data: {
              title: values.title || "",
              subtitle: values.subtitle || "",
              meta: values.meta || "",
              image: values.image || "",
            },
            design: {
              imageFill: values.imageFill as components["schemas"]["TitleWidget"]["config"]["design"]["imageFill"],
            },
          },
        }
        onUpdate(updatedData)
      },
    })
  }, [data, form, onUpdate])

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFileInputError(null)
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.type.startsWith("image/")) {
      setFileInputError("Please select a valid image file")
      return
    }

    if (file.size > IMAGE_SIZE_LIMIT_IN_MB) {
      setFileInputError("File size must be less than 5MB")
      return
    }

    const reader = new FileReader()
    reader.onload = (e) => {
      const base64String = e.target?.result as string
      form.setValue("image", base64String)
      setPreviewUrl(base64String)
    }
    reader.readAsDataURL(file)
  }

  const handleRemoveImage = () => {
    form.setValue("image", "")
    setPreviewUrl("")
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <Form {...form}>
      <form className="divide-y">
        <GeneralWidgetFields />
        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Content
          </Text>

          <Field control={form.control} name="title" label="Title">
            <Input />
          </Field>

          <Field control={form.control} name="subtitle" label="Subtitle">
            <Input />
          </Field>

          <Field control={form.control} name="meta" label="Meta">
            <Input />
          </Field>

          <div className="space-y-2">
            <div className="space-y-1">
              <Label htmlFor="image" className="text-sm font-medium">
                Image
              </Label>
              <Input id="image" ref={fileInputRef} type="file" accept="image/*" onChange={handleFileUpload} />
              {fileInputError && <Hint variant="error">{fileInputError}</Hint>}
            </div>

            {previewUrl && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Text size="small">Preview</Text>
                  <button type="button" onClick={handleRemoveImage} className="text-sm text-ui-button-danger">
                    Remove
                  </button>
                </div>
                <div className="rounded-lg border bg-ui-bg-subtle p-2">
                  <img
                    src={previewUrl}
                    alt="Preview"
                    className="mx-auto max-h-48 max-w-full rounded object-contain"
                    onError={() => {
                      setPreviewUrl("")
                      form.setValue("image", "")
                    }}
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Design
          </Text>
          <div className="space-y-4">
            <Form.Field
              control={form.control}
              name="style"
              render={({ field: { ref, onChange, ...field } }) => {
                return (
                  <Form.Item className="flex-row justify-between gap-4">
                    <Form.Label>Style</Form.Label>
                    <Form.Control>
                      <Select {...field} onValueChange={onChange}>
                        <Select.Trigger ref={ref} className="w-[170px]">
                          <Select.Value />
                        </Select.Trigger>
                        <Select.Content>
                          {styleOptions.map((item) => (
                            <Select.Item key={item.value} value={item.value}>
                              {item.label}
                            </Select.Item>
                          ))}
                        </Select.Content>
                      </Select>
                    </Form.Control>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />

            <Form.Field
              control={form.control}
              name="imageFill"
              render={({ field: { ref, onChange, ...field } }) => {
                return (
                  <Form.Item className="flex-row justify-between gap-4">
                    <Form.Label>Image Fill</Form.Label>
                    <Form.Control>
                      <Select {...field} onValueChange={onChange}>
                        <Select.Trigger ref={ref} className="w-[170px]">
                          <Select.Value />
                        </Select.Trigger>
                        <Select.Content>
                          {imageFillOptions.map((item) => (
                            <Select.Item key={item.value} value={item.value}>
                              {item.label}
                            </Select.Item>
                          ))}
                        </Select.Content>
                      </Select>
                    </Form.Control>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />
          </div>
        </div>
      </form>
    </Form>
  )
}
