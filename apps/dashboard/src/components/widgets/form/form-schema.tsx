import { z } from "zod"
import { generalWidgetEditorSchema } from "../general"

const baseFormFieldSchema = z.object({
  id: z.string(),
  isHidden: z.boolean(),
  label: z.string(),
  required: z.boolean(),
  hint: z.string().optional(),
  binding: z.string().optional(),
})

const textFieldSchema = baseFormFieldSchema.extend({
  type: z.literal("text"),
  validation: z
    .object({
      minLength: z.number().optional(),
      maxLength: z.number().optional(),
      pattern: z.string().optional(),
    })
    .optional(),
})

const longTextFieldSchema = baseFormFieldSchema.extend({
  type: z.literal("long_text"),
  validation: z
    .object({
      minLength: z.number().optional(),
      maxLength: z.number().optional(),
    })
    .optional(),
})

const dateTimeFieldSchema = baseFormFieldSchema.extend({
  type: z.literal("date_time"),
  validation: z
    .object({
      minDate: z.string().optional(),
      maxDate: z.string().optional(),
    })
    .optional(),
})

const dateFieldSchema = baseFormFieldSchema.extend({
  type: z.literal("date"),
  validation: z
    .object({
      minDate: z.string().optional(),
      maxDate: z.string().optional(),
    })
    .optional(),
})

const timeFieldSchema = baseFormFieldSchema.extend({
  type: z.literal("time"),
  validation: z
    .object({
      minTime: z.string().optional(),
      maxTime: z.string().optional(),
    })
    .optional(),
})

const currencyFieldSchema = baseFormFieldSchema.extend({
  type: z.literal("currency"),
  validation: z
    .object({
      min: z.number().optional(),
      max: z.number().optional(),
    })
    .optional(),
})

const numberFieldSchema = baseFormFieldSchema.extend({
  type: z.literal("number"),
  validation: z
    .object({
      min: z.number().optional(),
      max: z.number().optional(),
      isInteger: z.boolean().optional(),
    })
    .optional(),
})

const phoneFieldSchema = baseFormFieldSchema.extend({
  type: z.literal("phone"),
})

const emailFieldSchema = baseFormFieldSchema.extend({
  type: z.literal("email"),
})

const checkboxFieldSchema = baseFormFieldSchema.extend({
  type: z.literal("checkbox"),
  defaultValue: z.boolean().optional(),
})

const imagePickerFieldSchema = baseFormFieldSchema.extend({
  type: z.literal("image_picker"),
  allowMultiple: z.boolean().optional(),
})

const filePickerFieldSchema = baseFormFieldSchema.extend({
  type: z.literal("file_picker"),
  allowMultiple: z.boolean().optional(),
})

const optionSchema = z.object({
  label: z.string(),
  value: z.string(),
})

const dropdownFieldSchema = baseFormFieldSchema.extend({
  type: z.literal("dropdown"),
  options: z.array(optionSchema),
  allowMultiple: z.boolean().optional(),
})

const radioFieldSchema = baseFormFieldSchema.extend({
  type: z.literal("radio"),
  options: z.array(optionSchema),
})

const formFieldSchema = z.discriminatedUnion("type", [
  textFieldSchema,
  longTextFieldSchema,
  dateTimeFieldSchema,
  dateFieldSchema,
  timeFieldSchema,
  currencyFieldSchema,
  numberFieldSchema,
  phoneFieldSchema,
  emailFieldSchema,
  checkboxFieldSchema,
  imagePickerFieldSchema,
  filePickerFieldSchema,
  dropdownFieldSchema,
  radioFieldSchema,
])

const formFieldsSchema = z.array(formFieldSchema)

export const formEditorSchema = generalWidgetEditorSchema.merge(
  z.object({
    title: z.string().optional(),
    subtitle: z.string().optional(),
    submitButtonText: z.string().optional(),
    dataSourceId: z.string().optional(),
    externalApiId: z.string().optional(),
    fields: formFieldsSchema,
  }),
)

export type FormEditorSchema = z.infer<typeof formEditorSchema>
