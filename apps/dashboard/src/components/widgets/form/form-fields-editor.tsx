import { Form } from "@/components/common/form"
import { SortableList } from "@/components/common/sortable-list"
import { FieldSelectionInput } from "@/components/field-selection/field-selection-input"
import { cn } from "@/lib/utils"
import { DragEndEvent } from "@dnd-kit/core"
import { DotsSix } from "@medusajs/icons"
import { components } from "@saf/sdk"
import { Badge, Button, Checkbox, IconButton, Input, Popover, Switch, Text } from "@saf/ui"
import { IconChevronDown, IconChevronUp, IconPlus, IconTrash } from "@tabler/icons-react"
import { iconButtonVariants } from "@ui/components/icon-button"
import { useState } from "react"
import { useFieldArray, useFormContext } from "react-hook-form"
import { v4 as uuidv4 } from "uuid"
import { inlineInputWidth } from "../utils/constant"
import { formFieldOptions } from "./form-options"
import { FormEditorSchema } from "./form-schema"

type FormFieldSchema = FormEditorSchema["fields"][0]
type FormFieldSchemaType = FormFieldSchema["type"]

const ExpandableSection = ({ title, children }: { title: string; children: React.ReactNode }) => {
  const [isExpanded, setIsExpanded] = useState(true)

  return (
    <div className="mt-3 space-y-3 border-t border-ui-border-base pt-3">
      <button
        type="button"
        className="flex w-full items-center justify-between"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <Text size="small" weight="plus" className="text-ui-fg-subtle">
          {title}
        </Text>
        <div
          className={iconButtonVariants({
            size: "small",
            variant: "transparent",
          })}
        >
          {isExpanded ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
        </div>
      </button>

      {isExpanded && children}
    </div>
  )
}

const ValidationFields = ({ index, type }: { index: number; type: FormFieldSchemaType }) => {
  const formContext = useFormContext<FormEditorSchema>()

  switch (type) {
    case "text":
    case "long_text":
      return (
        <div className="flex flex-col gap-3">
          <Form.Field
            control={formContext.control}
            name={`fields.${index}.validation.minLength`}
            render={({ field }) => (
              <Form.Item>
                <Form.Label inline className="flex w-full">
                  Min Length
                  <Form.Control>
                    <Input
                      type="number"
                      min={0}
                      className={inlineInputWidth}
                      {...field}
                      onChange={(e) => field.onChange(e.target.valueAsNumber)}
                    />
                  </Form.Control>
                </Form.Label>
                <Form.ErrorMessage />
              </Form.Item>
            )}
          />
          <Form.Field
            control={formContext.control}
            name={`fields.${index}.validation.maxLength`}
            render={({ field }) => (
              <Form.Item>
                <Form.Label inline className="flex w-full">
                  Max Length
                  <Form.Control>
                    <Input
                      type="number"
                      min={0}
                      className={inlineInputWidth}
                      {...field}
                      onChange={(e) => field.onChange(e.target.valueAsNumber)}
                    />
                  </Form.Control>
                </Form.Label>
                <Form.ErrorMessage />
              </Form.Item>
            )}
          />
        </div>
      )

    case "number":
    case "currency":
      return (
        <div className="flex flex-col gap-3">
          <Form.Field
            control={formContext.control}
            name={`fields.${index}.validation.min`}
            render={({ field }) => (
              <Form.Item>
                <Form.Label inline className="flex w-full">
                  Min Value
                  <Form.Control>
                    <Input
                      type="number"
                      className={inlineInputWidth}
                      {...field}
                      onChange={(e) => field.onChange(e.target.valueAsNumber)}
                    />
                  </Form.Control>
                </Form.Label>
                <Form.ErrorMessage />
              </Form.Item>
            )}
          />
          <Form.Field
            control={formContext.control}
            name={`fields.${index}.validation.max`}
            render={({ field }) => (
              <Form.Item>
                <Form.Label inline className="flex w-full">
                  Max Value
                  <Form.Control>
                    <Input
                      type="number"
                      className={inlineInputWidth}
                      {...field}
                      onChange={(e) => field.onChange(e.target.valueAsNumber)}
                    />
                  </Form.Control>
                </Form.Label>
                <Form.ErrorMessage />
              </Form.Item>
            )}
          />
          {type === "number" && (
            <Form.Field
              control={formContext.control}
              name={`fields.${index}.validation.isInteger`}
              render={({ field: { value, onChange, ...field } }) => (
                <Form.Item>
                  <Form.Label inline className="flex w-full">
                    Integer Only
                    <Form.Control>
                      <Switch size="small" checked={value} onCheckedChange={onChange} {...field} />
                    </Form.Control>
                  </Form.Label>
                  <Form.ErrorMessage />
                </Form.Item>
              )}
            />
          )}
        </div>
      )

    case "date":
    case "date_time":
      return (
        <div className="flex flex-col gap-3">
          <Form.Field
            control={formContext.control}
            name={`fields.${index}.validation.minDate`}
            render={({ field }) => (
              <Form.Item>
                <Form.Label inline className="flex w-full">
                  Min Date
                  <Form.Control>
                    <Input type="date" className={inlineInputWidth} {...field} />
                  </Form.Control>
                </Form.Label>
                <Form.ErrorMessage />
              </Form.Item>
            )}
          />
          <Form.Field
            control={formContext.control}
            name={`fields.${index}.validation.maxDate`}
            render={({ field }) => (
              <Form.Item>
                <Form.Label inline className="flex w-full">
                  Max Date
                  <Form.Control>
                    <Input type="date" className={inlineInputWidth} {...field} />
                  </Form.Control>
                </Form.Label>
                <Form.ErrorMessage />
              </Form.Item>
            )}
          />
        </div>
      )

    case "time":
      return (
        <div className="flex flex-col gap-3">
          <Form.Field
            control={formContext.control}
            name={`fields.${index}.validation.minTime`}
            render={({ field }) => (
              <Form.Item>
                <Form.Label inline className="flex w-full">
                  Min Time
                  <Form.Control>
                    <Input type="time" className={inlineInputWidth} {...field} />
                  </Form.Control>
                </Form.Label>
                <Form.ErrorMessage />
              </Form.Item>
            )}
          />
          <Form.Field
            control={formContext.control}
            name={`fields.${index}.validation.maxTime`}
            render={({ field }) => (
              <Form.Item>
                <Form.Label inline className="flex w-full">
                  Max Time
                  <Form.Control>
                    <Input type="time" className={inlineInputWidth} {...field} />
                  </Form.Control>
                </Form.Label>
                <Form.ErrorMessage />
              </Form.Item>
            )}
          />
        </div>
      )

    default:
      return null
  }
}

const FieldSpecificSettings = ({ index, type }: { index: number; type: FormFieldSchemaType }) => {
  const formContext = useFormContext<FormEditorSchema>()

  switch (type) {
    case "image_picker":
    case "file_picker":
      return (
        <ExpandableSection title="Field Settings">
          <Form.Field
            control={formContext.control}
            name={`fields.${index}.allowMultiple`}
            render={({ field: { value, onChange, ...field } }) => (
              <Form.Item>
                <Form.Label inline className="flex w-full">
                  Allow Multiple
                  <Form.Control>
                    <Switch size="small" checked={value} onCheckedChange={onChange} {...field} />
                  </Form.Control>
                </Form.Label>
                <Form.ErrorMessage />
              </Form.Item>
            )}
          />
        </ExpandableSection>
      )

    case "checkbox":
      return (
        <ExpandableSection title="Field Settings">
          <Form.Field
            control={formContext.control}
            name={`fields.${index}.defaultValue`}
            render={({ field: { value, onChange, ...field } }) => (
              <Form.Item>
                <Form.Label inline className="flex w-full">
                  Default Value
                  <Form.Control>
                    <Switch size="small" checked={value} onCheckedChange={onChange} {...field} />
                  </Form.Control>
                </Form.Label>
                <Form.ErrorMessage />
              </Form.Item>
            )}
          />
        </ExpandableSection>
      )

    case "dropdown":
      return (
        <>
          <ExpandableSection title="Field Settings">
            <Form.Field
              control={formContext.control}
              name={`fields.${index}.allowMultiple`}
              render={({ field: { value, onChange, ...field } }) => (
                <Form.Item>
                  <Form.Label inline className="flex w-full">
                    Allow Multiple
                    <Form.Control>
                      <Switch size="small" checked={value} onCheckedChange={onChange} {...field} />
                    </Form.Control>
                  </Form.Label>
                  <Form.ErrorMessage />
                </Form.Item>
              )}
            />
          </ExpandableSection>
          <OptionsEditor index={index} />
        </>
      )

    case "radio":
      return <OptionsEditor index={index} />

    default:
      if (["text", "long_text", "number", "currency", "date", "date_time", "time"].includes(type)) {
        return (
          <ExpandableSection title="Validation">
            <ValidationFields index={index} type={type} />
          </ExpandableSection>
        )
      }
      return null
  }
}

const OptionsEditor = ({ index }: { index: number }) => {
  const formContext = useFormContext<FormEditorSchema>()
  const { fields, append, remove } = useFieldArray({
    control: formContext.control,
    name: `fields.${index}.options`,
  })

  return (
    <ExpandableSection title="Options">
      <div className="flex flex-col gap-3">
        <div className="space-y-2">
          {fields?.map((option, optionIndex) => (
            <div key={option.id} className="flex items-center gap-2">
              <Form.Field
                control={formContext.control}
                name={`fields.${index}.options.${optionIndex}.label`}
                render={({ field }) => <Input placeholder="Label" className="flex-1" {...field} />}
              />
              <Form.Field
                control={formContext.control}
                name={`fields.${index}.options.${optionIndex}.value`}
                render={({ field }) => <Input placeholder="Value" className="flex-1" {...field} />}
              />
              <IconButton type="button" variant="transparent" size="small" onClick={() => remove(optionIndex)}>
                <IconTrash size={16} />
              </IconButton>
            </div>
          ))}
        </div>
        <div className="flex items-center justify-between">
          {!fields.length ? (
            <Text size="small" className="text-ui-fg-subtle">
              No options added
            </Text>
          ) : null}
          <Button
            type="button"
            size="small"
            className="ms-auto"
            variant="secondary"
            onClick={() => append({ label: "", value: "" })}
          >
            <IconPlus size={14} className="mr-1" />
            Add Option
          </Button>
        </div>
      </div>
    </ExpandableSection>
  )
}

export const FormFieldsEditor = ({ bindingSchema }: { bindingSchema: any }) => {
  const [fieldSelectorOpen, setFieldSelectorOpen] = useState(false)
  const [isLoading, setIsLoading] = useState(false)

  const formContext = useFormContext<FormEditorSchema>()

  const { fields, append, remove, move } = useFieldArray({
    control: formContext.control,
    name: "fields",
  })

  const handleAddField = (fieldType: FormFieldSchemaType, label: string) => {
    setIsLoading(true)
    try {
      const baseField: components["schemas"]["BaseFormField"] = {
        id: uuidv4(),
        isHidden: false,
        label,
        required: false,
        hint: "",
        binding: "",
      }

      let newField: FormFieldSchema

      // Create field based on type with appropriate defaults
      switch (fieldType) {
        case "text":
        case "long_text":
          newField = {
            ...baseField,
            type: fieldType,
            validation: {
              minLength: undefined,
              maxLength: undefined,
            },
          }
          break
        case "number":
          newField = {
            ...baseField,
            type: fieldType,
            validation: {
              min: undefined,
              max: undefined,
              isInteger: false,
            },
          }
          break
        case "currency":
          newField = {
            ...baseField,
            type: fieldType,
            validation: {
              min: undefined,
              max: undefined,
            },
          }
          break
        case "date":
        case "date_time":
          newField = {
            ...baseField,
            type: fieldType,
            validation: {
              minDate: undefined,
              maxDate: undefined,
            },
          }
          break
        case "time":
          newField = {
            ...baseField,
            type: fieldType,
            validation: {
              minTime: undefined,
              maxTime: undefined,
            },
          }
          break
        case "checkbox":
          newField = {
            ...baseField,
            type: fieldType,
            defaultValue: false,
          }
          break
        case "dropdown":
        case "radio":
          newField = {
            ...baseField,
            type: fieldType,
            options: [],
            ...(fieldType === "dropdown" ? { allowMultiple: false } : {}),
          }
          break
        case "image_picker":
        case "file_picker":
          newField = {
            ...baseField,
            type: fieldType,
            allowMultiple: false,
          }
          break
        default:
          newField = {
            ...baseField,
            type: fieldType,
          }
      }

      append(newField)
      setFieldSelectorOpen(false)
    } finally {
      setIsLoading(false)
    }
  }

  const handleRemoveField = (index: number) => {
    remove(index)
  }

  const handleReorderFields = (_: any, dragEndEvent: DragEndEvent) => {
    const { active, over } = dragEndEvent

    if (!over || active.id === over.id) return

    const activeIndex = fields.findIndex((field) => field.id === active.id)
    const overIndex = fields.findIndex((field) => field.id === over.id)

    if (activeIndex === -1 || overIndex === -1) return

    move(activeIndex, overIndex)
  }

  return (
    <div className="flex flex-col gap-4">
      <Text className="px-4 uppercase" size="xsmall" weight="plus">
        Form Fields
      </Text>

      <div className="flex flex-col gap-4">
        {fields.length === 0 ? (
          <Text className="py-2 text-center" size="xsmall" color="subtle">
            No form fields added yet. Click the + button to add fields.
          </Text>
        ) : (
          <SortableList
            className="gap-2"
            items={fields.map((field, index) => ({ id: field.id, index }))}
            onChange={handleReorderFields}
            renderItem={(item) => {
              const fieldIndex = item.index
              const field = fields[fieldIndex]

              return (
                <SortableList.Item id={item.id} key={item.id}>
                  <FormFieldItem
                    field={field}
                    index={fieldIndex}
                    onRemove={() => handleRemoveField(fieldIndex)}
                    bindingSchema={bindingSchema}
                  />
                </SortableList.Item>
              )
            }}
          />
        )}
      </div>

      <div className="flex justify-center">
        <Popover open={fieldSelectorOpen} onOpenChange={setFieldSelectorOpen}>
          <Popover.Trigger asChild>
            <Button size="small" disabled={isLoading}>
              <IconPlus size={16} className="mr-1" />
              Add Field
              {isLoading && <span className="ml-2 h-4 w-4 animate-spin">⟳</span>}
            </Button>
          </Popover.Trigger>
          <Popover.Content className="w-[320px] p-0" align="end">
            <FormFieldSelector onAddField={handleAddField} />
          </Popover.Content>
        </Popover>
      </div>
    </div>
  )
}

const FormFieldSelector = ({ onAddField }: { onAddField: (type: FormFieldSchemaType, label: string) => void }) => {
  return (
    <div className="max-h-[400px] divide-y overflow-y-auto py-2">
      <div className="py-2">
        <Text size="xsmall" className="px-4 uppercase" weight="plus">
          Form Inputs
        </Text>
        <div className="grid grid-cols-2 gap-2 px-1 py-2">
          {formFieldOptions.map((option) => (
            <FieldSelectionButton
              key={option.type}
              label={option.label}
              onClick={() => onAddField(option.type, option.label)}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

const FieldSelectionButton = ({ label, onClick }: { label: string; onClick: () => void }) => (
  <button
    type="button"
    onClick={onClick}
    className={cn(
      "flex w-full flex-col items-center justify-start gap-1.5 rounded-lg border border-ui-border-base bg-ui-bg-subtle p-2 text-center transition-colors hover:bg-ui-bg-base-hover active:bg-ui-bg-base-pressed",
    )}
  >
    <div className="text-xs font-medium">{label}</div>
  </button>
)

const FormFieldItem = ({
  field,
  index,
  bindingSchema,
  onRemove,
}: {
  field: FormFieldSchema
  index: number
  bindingSchema: any
  onRemove: () => void
}) => {
  const { attributes, listeners, ref } = SortableList.useSortableItemContext()
  const formContext = useFormContext<FormEditorSchema>()

  const getFieldTypeLabel = (type: string) => {
    const option = formFieldOptions.find((opt) => opt.type === type)
    return option?.label || type?.replace("_", " ")
  }

  return (
    <div className="mx-2 w-full rounded-lg border bg-ui-bg-base">
      <div className="flex items-center gap-2 p-3">
        <div
          className={cn(iconButtonVariants({ variant: "transparent", size: "small" }), "cursor-grab")}
          ref={ref}
          {...attributes}
          {...listeners}
        >
          <DotsSix className="h-4 w-4" />
        </div>
        <Badge size="xsmall" color="blue">
          {getFieldTypeLabel(field.type)}
        </Badge>
        <Form.Field
          control={formContext.control}
          name={`fields.${index}.required`}
          render={({ field: { value, onChange, ...field } }) => {
            return (
              <Form.Item className="ms-auto">
                <Form.Label inline className="gap-2">
                  <Form.Control>
                    <Checkbox checked={value} onCheckedChange={onChange} {...field} />
                  </Form.Control>
                  Required
                </Form.Label>
                <Form.ErrorMessage />
              </Form.Item>
            )
          }}
        />
        <IconButton type="button" variant="transparent" size="small" onClick={onRemove}>
          <IconTrash className="h-4 w-4" />
        </IconButton>
      </div>

      <div className="space-y-3 border-t p-3">
        <Form.Field
          control={formContext.control}
          name={`fields.${index}.label`}
          render={({ field }) => {
            return (
              <Form.Item>
                <Form.Label inline>
                  Label
                  <Form.Control>
                    <Input
                      value={field.value || ""}
                      onChange={(e) => field.onChange(e.target.value)}
                      className={inlineInputWidth}
                      placeholder="Enter field label"
                    />
                  </Form.Control>
                </Form.Label>
                <Form.ErrorMessage />
              </Form.Item>
            )
          }}
        />
        <Form.Field
          control={formContext.control}
          name={`fields.${index}.binding`}
          render={({ field: { onChange, ...field } }) => {
            return (
              <Form.Item>
                <Form.Label inline>
                  Binding
                  <Form.Control>
                    <FieldSelectionInput
                      placeholder="e.g., body.userName..."
                      schema={bindingSchema}
                      {...field}
                      className={inlineInputWidth}
                      isTemplated={false}
                      onChange={onChange}
                    />
                  </Form.Control>
                </Form.Label>
                <Form.ErrorMessage />
              </Form.Item>
            )
          }}
        />
        <Form.Field
          control={formContext.control}
          name={`fields.${index}.hint`}
          render={({ field }) => {
            return (
              <Form.Item>
                <Form.Label inline>
                  Hint
                  <Form.Control>
                    <Input
                      value={field.value || ""}
                      onChange={(e) => field.onChange(e.target.value)}
                      className={inlineInputWidth}
                      placeholder="Enter field hint"
                    />
                  </Form.Control>
                </Form.Label>
                <Form.ErrorMessage />
              </Form.Item>
            )
          }}
        />
        <FieldSpecificSettings index={index} type={field.type} />
      </div>
    </div>
  )
}
