import { safQuery } from "@/client"
import { showHumanFriendlyError } from "@/lib/is-validation-error"
import { ErrorMessage } from "@hookform/error-message"
import { components } from "@saf/sdk"
import { Button, Checkbox, Hint, Input, Label, RadioGroup, Text, Textarea, toast } from "@saf/ui"
import { inputVariants } from "@ui/components/input"
import { useEffect, useRef } from "react"
import { Control, Controller, FieldErrors, FieldValues, SubmitHandler, useForm } from "react-hook-form"
import { FormFields } from "./form-options"

type TextField = components["schemas"]["TextField"]
type LongTextField = components["schemas"]["LongTextField"]
type NumberField = components["schemas"]["NumberField"]
type CheckboxField = components["schemas"]["CheckboxField"]
type DateField = components["schemas"]["DateField"]
type TimeField = components["schemas"]["TimeField"]
type DateTimeField = components["schemas"]["DateTimeField"]
type CurrencyField = components["schemas"]["CurrencyField"]
type DropdownField = components["schemas"]["DropdownField"]
type RadioField = components["schemas"]["RadioField"]
type ImagePickerField = components["schemas"]["ImagePickerField"]
type FilePickerField = components["schemas"]["FilePickerField"]

const FormField = ({
  field,
  control,
  errors,
}: {
  field: FormFields[0]
  control: Control<FieldValues, any, FieldValues>
  errors: FieldErrors<FieldValues>
}) => {
  if (field.isHidden) return null

  const renderLabel = () =>
    field.label ? (
      <Label htmlFor={field.id} className="mb-2 block">
        {field.label}
        {field.required && <span className="ml-1 text-ui-button-danger">*</span>}
      </Label>
    ) : null

  const renderHint = () => (field.hint ? <Hint>{field.hint}</Hint> : null)

  const renderError = () => (
    <ErrorMessage
      errors={errors}
      name={field.id}
      render={({ message }) => <Text className="mt-2 text-ui-button-danger">{message}</Text>}
    />
  )

  switch (field.type) {
    case "text":
      return (
        <div className="px-4">
          <Controller
            name={field.id}
            control={control}
            rules={{
              required: field.required ? "This field is required" : false,
              minLength: (field as TextField).validation?.minLength && {
                value: (field as TextField).validation?.minLength as number,
                message: `Minimum length is ${(field as TextField).validation?.minLength} characters`,
              },
              maxLength: (field as TextField).validation?.maxLength && {
                value: (field as TextField).validation?.maxLength as number,
                message: `Maximum length is ${(field as TextField).validation?.maxLength} characters`,
              },
            }}
            render={({ field: { ref, ...inputProps } }) => (
              <>
                {renderLabel()}
                <Input {...inputProps} id={field.id} />
                {renderHint()}
                {renderError()}
              </>
            )}
          />
        </div>
      )
    case "long_text":
      return (
        <div className="px-4">
          <Controller
            name={field.id}
            control={control}
            rules={{
              required: field.required ? "This field is required" : false,
              minLength: (field as LongTextField).validation?.minLength && {
                value: (field as LongTextField).validation?.minLength as number,
                message: `Minimum length is ${(field as LongTextField).validation?.minLength} characters`,
              },
              maxLength: (field as LongTextField).validation?.maxLength && {
                value: (field as LongTextField).validation?.maxLength as number,
                message: `Maximum length is ${(field as LongTextField).validation?.maxLength} characters`,
              },
            }}
            render={({ field: { ref, ...inputProps } }) => (
              <>
                {renderLabel()}
                <Textarea {...inputProps} id={field.id} />
                {renderHint()}
                {renderError()}
              </>
            )}
          />
        </div>
      )
    case "email":
      return (
        <div className="px-4">
          <Controller
            name={field.id}
            control={control}
            rules={{
              required: field.required ? "This field is required" : false,
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: "Invalid email address",
              },
            }}
            render={({ field: { ref, ...inputProps } }) => (
              <>
                {renderLabel()}
                <Input {...inputProps} type="email" id={field.id} />
                {renderHint()}
                {renderError()}
              </>
            )}
          />
        </div>
      )
    case "number":
      return (
        <div className="px-4">
          <Controller
            name={field.id}
            control={control}
            rules={{
              required: field.required ? "This field is required" : false,
              min: (field as NumberField).validation?.min && {
                value: (field as NumberField).validation?.min as number,
                message: `Minimum value is ${(field as NumberField).validation?.min}`,
              },
              max: (field as NumberField).validation?.max && {
                value: (field as NumberField).validation?.max as number,
                message: `Maximum value is ${(field as NumberField).validation?.max}`,
              },
            }}
            render={({ field: { ref, onChange, ...inputProps } }) => (
              <>
                {renderLabel()}
                <Input
                  {...inputProps}
                  type="number"
                  id={field.id}
                  min={(field as NumberField).validation?.min}
                  max={(field as NumberField).validation?.max}
                  step={(field as NumberField).validation?.isInteger ? 1 : undefined}
                  onChange={(e) => onChange(e.target.valueAsNumber)}
                />
                {renderHint()}
                {renderError()}
              </>
            )}
          />
        </div>
      )
    case "checkbox":
      return (
        <div className="px-4">
          <Controller
            name={field.id}
            control={control}
            defaultValue={(field as CheckboxField).defaultValue || false}
            rules={{ required: field.required ? "This field is required" : false }}
            render={({ field: { value, onChange, ...props } }) => (
              <div>
                <Label className="flex items-center gap-2">
                  <Checkbox id={field.id} checked={value} onCheckedChange={onChange} {...props} />
                  {field.label || "Checkbox Field"}
                  {field.required && <span className="ml-1 text-ui-button-danger">*</span>}
                </Label>
                {renderError()}
                {renderHint()}
              </div>
            )}
          />
        </div>
      )
    case "date":
      return (
        <div className="px-4">
          <Controller
            name={field.id}
            control={control}
            rules={{
              required: field.required ? "This field is required" : false,
            }}
            render={({ field: { ref, ...inputProps } }) => (
              <>
                {renderLabel()}
                <Input
                  {...inputProps}
                  type="date"
                  id={field.id}
                  min={(field as DateField).validation?.minDate}
                  max={(field as DateField).validation?.maxDate}
                />
                {renderHint()}
                {renderError()}
              </>
            )}
          />
        </div>
      )
    case "time":
      return (
        <div className="px-4">
          <Controller
            name={field.id}
            control={control}
            rules={{
              required: field.required ? "This field is required" : false,
            }}
            render={({ field: { ref, ...inputProps } }) => (
              <>
                {renderLabel()}
                <Input
                  {...inputProps}
                  type="time"
                  id={field.id}
                  min={(field as TimeField).validation?.minTime}
                  max={(field as TimeField).validation?.maxTime}
                />
                {renderHint()}
                {renderError()}
              </>
            )}
          />
        </div>
      )
    case "date_time":
      return (
        <div className="px-4">
          <Controller
            name={field.id}
            control={control}
            rules={{
              required: field.required ? "This field is required" : false,
            }}
            render={({ field: { ref, ...inputProps } }) => (
              <>
                {renderLabel()}
                <Input
                  {...inputProps}
                  type="datetime-local"
                  id={field.id}
                  min={(field as DateTimeField).validation?.minDate}
                  max={(field as DateTimeField).validation?.maxDate}
                />
                {renderHint()}
                {renderError()}
              </>
            )}
          />
        </div>
      )
    case "phone":
      return (
        <div className="px-4">
          <Controller
            name={field.id}
            control={control}
            rules={{
              required: field.required ? "This field is required" : false,
              pattern: {
                value: /^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/,
                message: "Invalid phone number format",
              },
            }}
            render={({ field: { ref, ...inputProps } }) => (
              <>
                {renderLabel()}
                <Input {...inputProps} type="tel" id={field.id} />
                {renderHint()}
                {renderError()}
              </>
            )}
          />
        </div>
      )
    case "currency":
      return (
        <div className="px-4">
          <Controller
            name={field.id}
            control={control}
            rules={{
              required: field.required ? "This field is required" : false,
              min: (field as CurrencyField).validation?.min && {
                value: (field as CurrencyField).validation?.min as number,
                message: `Minimum value is ${(field as CurrencyField).validation?.min}`,
              },
              max: (field as CurrencyField).validation?.max && {
                value: (field as CurrencyField).validation?.max as number,
                message: `Maximum value is ${(field as CurrencyField).validation?.max}`,
              },
            }}
            render={({ field: { ref, onChange, ...inputProps } }) => (
              <>
                {renderLabel()}
                <Input
                  {...inputProps}
                  type="number"
                  id={field.id}
                  step="0.01"
                  min={(field as CurrencyField).validation?.min}
                  max={(field as CurrencyField).validation?.max}
                  onChange={(e) => onChange(e.target.valueAsNumber)}
                />
                {renderHint()}
                {renderError()}
              </>
            )}
          />
        </div>
      )
    case "dropdown":
      return (
        <div className="px-4">
          <Controller
            name={field.id}
            control={control}
            defaultValue={undefined}
            rules={{
              required: field.required ? "This field is required" : false,
            }}
            render={({ field: { ref, ...inputProps } }) => (
              <>
                {renderLabel()}
                <select
                  {...inputProps}
                  id={field.id}
                  multiple={(field as DropdownField).allowMultiple}
                  className={inputVariants({
                    className: "h-auto",
                  })}
                >
                  <option value="" disabled={field.required} selected>
                    Select an option
                  </option>
                  {(field as DropdownField).options?.map((option) => (
                    <option key={option.value} value={option.value}>
                      {option.label}
                    </option>
                  ))}
                </select>
                {renderHint()}
                {renderError()}
              </>
            )}
          />
        </div>
      )
    case "radio":
      return (
        <div className="px-4">
          <Controller
            name={field.id}
            control={control}
            rules={{
              required: field.required ? "This field is required" : false,
            }}
            render={({ field: { value, onChange, ...inputProps } }) => (
              <>
                <RadioGroup value={value} onValueChange={onChange} {...inputProps}>
                  <Label>{field.label}</Label>
                  <div className="space-y-2">
                    {(field as RadioField).options?.map((option) => (
                      <div key={option.value} className="flex items-center gap-2">
                        <RadioGroup.Item value={option.value} id={`${field.id}-${option.value}`} />
                        <Label htmlFor={`${field.id}-${option.value}`}>{option.label}</Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
                {renderHint()}
                {renderError()}
              </>
            )}
          />
        </div>
      )
    case "image_picker":
      return (
        <div className="px-4">
          <Controller
            name={field.id}
            control={control}
            rules={{
              required: field.required ? "This field is required" : false,
            }}
            render={({ field: { ref, onChange, value, ...inputProps } }) => (
              <>
                {renderLabel()}
                <Input
                  {...inputProps}
                  id={field.id}
                  type="file"
                  accept="image/*"
                  multiple={(field as ImagePickerField).allowMultiple}
                  onChange={(e) => {
                    onChange(e.target.files)
                  }}
                />
                {renderHint()}
                {renderError()}
              </>
            )}
          />
        </div>
      )
    case "file_picker":
      return (
        <div className="px-4">
          <Controller
            name={field.id}
            control={control}
            rules={{ required: field.required ? "This field is required" : false }}
            render={({ field: { ref, onChange, value, ...inputProps } }) => (
              <>
                {renderLabel()}
                <Input
                  type="file"
                  multiple={(field as FilePickerField).allowMultiple}
                  {...inputProps}
                  id={field.id}
                  onChange={(e) => {
                    onChange(e.target.files)
                  }}
                />
                {renderHint()}
                {renderError()}
              </>
            )}
          />
        </div>
      )
    default:
      return null
  }
}

export const FormWidget = ({ data }: { data: components["schemas"]["FormWidget"] }) => {
  const { title, subtitle, fields, design } = data.config
  const submitButtonText = design?.submitButtonText || "Submit"

  // Keep track of previous fields to detect changes
  const previousFieldsRef = useRef<FormFields>([])

  const {
    handleSubmit,
    control,
    formState: { isSubmitting, errors },
    unregister,
  } = useForm()

  // Effect to clean up removed fields
  useEffect(() => {
    // Skip on first render
    if (previousFieldsRef.current.length === 0) {
      previousFieldsRef.current = fields || []
      return
    }

    // Find fields that were in the previous configuration but not in the current one
    const currentFieldIds = new Set((fields || []).map((field) => field.id))
    const removedFields = previousFieldsRef.current
      .filter((field) => !currentFieldIds.has(field.id))
      .map((field) => field.id)

    // Unregister removed fields to clean up form state
    if (removedFields.length > 0) {
      unregister(removedFields)
    }

    // Update reference for next comparison
    previousFieldsRef.current = fields || []
  }, [fields, unregister])

  const submitData = safQuery.useMutation("post", "/api/customer/submit-form/{widgetId}")

  const onSubmit: SubmitHandler<Record<string, any>> = (formData) => {
    // Filter out any potential stale values that might still be in formData
    // but are not in the current fields configuration
    const currentFieldIds = new Set((fields || []).map((field) => field.id))
    const cleanedFormData = Object.entries(formData).reduce(
      (acc, [key, value]) => {
        if (currentFieldIds.has(key)) {
          acc[key] = value
        }
        return acc
      },
      {} as Record<string, any>,
    )

    submitData.mutate(
      {
        params: {
          path: { widgetId: data.id },
        },
        body: {
          formData: cleanedFormData,
        },
      },
      {
        onSuccess: () => {
          toast.success("Form submitted successfully")
        },
        onError: (error) => {
          toast.error(showHumanFriendlyError(error))
        },
      },
    )
  }

  return (
    <div className="py-2">
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {(title || subtitle) && (
          <div className="space-y-1 px-4">
            {title && (
              <Text size="xlarge" weight="plus">
                {title}
              </Text>
            )}
            {subtitle && (
              <Text size="base" className="text-ui-fg-subtle">
                {subtitle}
              </Text>
            )}
          </div>
        )}

        <div className="space-y-4">
          {fields?.map((field) => (
            <div key={field.id}>
              <FormField field={field} control={control} errors={errors} />
            </div>
          ))}
        </div>

        <div className="px-4 pt-2">
          <Button type="submit" className="w-full" isLoading={isSubmitting}>
            {submitButtonText}
          </Button>
        </div>
      </form>
    </div>
  )
}
