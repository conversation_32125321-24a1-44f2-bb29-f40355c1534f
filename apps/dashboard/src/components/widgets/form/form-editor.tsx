import { safQuery } from "@/client"
import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { Loader } from "@/components/common/loader"
import { zodResolver } from "@hookform/resolvers/zod"
import { components } from "@saf/sdk"
import { IconButton, Input, Select, Text } from "@saf/ui"
import { IconChevronDown, IconChevronUp } from "@tabler/icons-react"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { useParams } from "react-router-dom"
import { useDeepCompareEffect } from "use-deep-compare"
import { GeneralWidgetFields } from "../general"
import { FormFieldsEditor } from "./form-fields-editor"
import { formEditorSchema, FormEditorSchema } from "./form-schema"

export const FormEditor = ({
  data,
  onUpdate,
}: {
  data: components["schemas"]["FormWidget"]
  onUpdate: (updatedData: components["schemas"]["FormWidget"]) => void
}) => {
  const { teamId = "", miniAppId = "" } = useParams()
  const [expandContent, setExpandContent] = useState(false)

  const [initialRender, setInitialRender] = useState(true)

  const form = useForm<FormEditorSchema>({
    resolver: zodResolver(formEditorSchema),
    defaultValues: {
      name: "",
      isHidden: false,
      title: "",
      subtitle: "",
      submitButtonText: "Submit",
      dataSourceId: undefined,
      externalApiId: undefined,
      fields: [],
    },
  })

  const { data: dataSourcesData, isLoading: isLoadingDataSources } = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
          miniAppId: parseInt(miniAppId),
        },
        query: {
          page: "1",
          limit: "200",
        },
      },
    },
    {
      enabled: teamId != "" && miniAppId != "",
    },
  )

  const selectedDataSourceId = form.watch("dataSourceId")

  const { data: externalApisData, isLoading: isLoadingExernalApi } = safQuery.useQuery(
    "get",
    "/api/admin/teams/{teamId}/mini-apps/{miniAppId}/data-sources/{dataSourceId}/external-apis",
    {
      params: {
        path: {
          teamId: parseInt(teamId),
          miniAppId: parseInt(miniAppId),
          dataSourceId: parseInt(selectedDataSourceId ?? ""),
        },
        query: {
          page: "1",
          limit: "200",
        },
      },
    },
    {
      enabled: teamId != "" && miniAppId != "" && selectedDataSourceId != "" && selectedDataSourceId != null,
    },
  )

  const selectedExternalApiId = form.watch("externalApiId")

  const externalApiConfig =
    selectedExternalApiId != null
      ? externalApisData?.items?.find((api) => api.id.toString() === selectedExternalApiId)?.config
      : undefined

  useDeepCompareEffect(() => {
    if (data && initialRender) {
      form.reset({
        name: data.name || "",
        isHidden: data.isHidden ?? false,
        title: data.config.title || "",
        subtitle: data.config.subtitle || "",
        submitButtonText: data.config.design?.submitButtonText || "Submit",
        dataSourceId: data.config.bindingConfig?.dataSourceId?.toString() || undefined,
        externalApiId: data.config.bindingConfig?.externalApiId?.toString() || undefined,
        fields: data.config.fields,
      })
      setInitialRender(false)
    }
  }, [data, form, initialRender])

  useEffect(() => {
    if (!data) return

    return form.subscribe({
      name: "",
      formState: {
        values: true,
      },
      callback({ values }) {
        const updatedData: components["schemas"]["FormWidget"] = {
          ...data,
          name: values.name || "",
          isHidden: values.isHidden ?? false,
          config: {
            ...data.config,
            title: values.title,
            subtitle: values.subtitle,
            bindingConfig: values.dataSourceId
              ? {
                  dataSourceId: values.dataSourceId != null ? parseInt(values.dataSourceId) : undefined,
                  externalApiId: values.externalApiId != null ? parseInt(values.externalApiId) : undefined,
                }
              : undefined,
            design: {
              submitButtonText: values.submitButtonText,
            },
            fields: values.fields,
          },
        }
        onUpdate(updatedData)
      },
    })
  }, [data, form, onUpdate])

  const dataSourceOptions =
    dataSourcesData?.items?.map((ds) => ({
      label: ds.name,
      value: ds.id.toString(),
    })) || []

  const externalApiOptions =
    externalApisData?.items?.map((api) => ({
      label: api.name,
      value: api.id.toString(),
    })) || []

  return (
    <Form {...form}>
      <form className="divide-y">
        <GeneralWidgetFields />

        <div className="flex flex-col gap-4 p-4">
          <div className="flex items-center justify-between">
            <Text className="uppercase" size="xsmall" weight="plus">
              Content
            </Text>
            <IconButton type="button" size="xsmall" variant="transparent" onClick={() => setExpandContent((v) => !v)}>
              {expandContent ? <IconChevronUp size={16} /> : <IconChevronDown size={16} />}
            </IconButton>
          </div>

          {expandContent && (
            <>
              <Field control={form.control} name="title" label="Form Title">
                <Input placeholder="Enter form title..." />
              </Field>

              <Field control={form.control} name="subtitle" label="Form Subtitle">
                <Input placeholder="Enter form subtitle..." />
              </Field>

              <Field control={form.control} name="submitButtonText" label="Submit Button Text">
                <Input placeholder="Submit" />
              </Field>
            </>
          )}
        </div>

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Data Binding
          </Text>

          <Form.Field
            control={form.control}
            name="dataSourceId"
            render={({ field: { ref, onChange, ...field } }) => {
              return (
                <Form.Item className="flex-row justify-between gap-4">
                  <Form.Label className="flex items-center gap-2">
                    Data Source
                    {isLoadingDataSources && <Loader size="small" />}
                  </Form.Label>
                  <Form.Control>
                    <Select
                      {...field}
                      onValueChange={(value) => {
                        if (value == null || value == "") return
                        onChange(value)
                        form.setValue("externalApiId", undefined)
                      }}
                    >
                      <Select.Trigger ref={ref} className="w-[170px]">
                        <Select.Value placeholder="Select data source..." />
                      </Select.Trigger>
                      <Select.Content>
                        {dataSourceOptions.map((item) => (
                          <Select.Item key={item.value} value={item.value}>
                            {item.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  </Form.Control>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />

          {selectedDataSourceId != null && (
            <Form.Field
              control={form.control}
              name="externalApiId"
              render={({ field: { ref, onChange, ...field } }) => {
                return (
                  <Form.Item className="flex-row justify-between gap-4">
                    <Form.Label className="flex items-center gap-2">
                      External API
                      {isLoadingExernalApi && <Loader size="small" />}
                    </Form.Label>

                    <Form.Control>
                      <Select
                        {...field}
                        onValueChange={(value) => {
                          if (value == null || value == "") return
                          onChange(value)
                        }}
                      >
                        <Select.Trigger ref={ref} className="w-[170px]">
                          <Select.Value placeholder="Select external API..." />
                        </Select.Trigger>
                        <Select.Content>
                          {externalApiOptions.map((item) => (
                            <Select.Item key={item.value} value={item.value}>
                              {item.label}
                            </Select.Item>
                          ))}
                        </Select.Content>
                      </Select>
                    </Form.Control>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />
          )}
        </div>

        <div className="py-4">
          <FormFieldsEditor bindingSchema={externalApiConfig} />
        </div>
      </form>
    </Form>
  )
}
