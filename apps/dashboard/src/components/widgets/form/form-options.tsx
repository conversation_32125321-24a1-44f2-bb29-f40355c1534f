import { components } from "@saf/sdk"

export type FormFields = components["schemas"]["FormWidget"]["config"]["fields"]

export type FormFieldType = FormFields[0]["type"]

export type FormFieldOption = {
  label: string
  type: FormFieldType
}

export const formFieldOptions: FormFieldOption[] = [
  { label: "Text Field", type: "text" },
  { label: "Long Text Field", type: "long_text" },
  { label: "Date Time Picker", type: "date_time" },
  { label: "Date Picker", type: "date" },
  { label: "Time Picker", type: "time" },
  { label: "Currency Input", type: "currency" },
  { label: "Number Input", type: "number" },
  { label: "Phone Input", type: "phone" },
  { label: "Email Input", type: "email" },
  { label: "Checkbox", type: "checkbox" },
  { label: "Image Picker", type: "image_picker" },
  { label: "File Picker", type: "file_picker" },
  { label: "Dropdown", type: "dropdown" },
  { label: "Radio", type: "radio" },
]
