import { Form } from "@/components/common/form"
import { Options } from "@/configs"
import { createEnumFromOptions } from "@/lib/conversion"
import { zodResolver } from "@hookform/resolvers/zod"
import { components } from "@saf/sdk"
import { Checkbox, Hint, Input, Select, Text } from "@saf/ui"
import { useEffect, useRef, useState } from "react"
import { useForm } from "react-hook-form"
import { useDeepCompareEffect } from "use-deep-compare"
import { z } from "zod"
import { generalWidgetEditorSchema, GeneralWidgetFields } from "../general"

export const IMAGE_SIZE_LIMIT_IN_MB = 5 * 1024 * 1024

const aspectRatioOptions: Options = [
  { label: "1:1", value: "1:1" },
  { label: "16:9", value: "16:9" },
  { label: "4:3", value: "4:3" },
  { label: "3:2", value: "3:2" },
  { label: "Auto", value: "auto" },
]

const fillOptions: Options = [
  { label: "Fill", value: "fill" },
  { label: "Contain", value: "contain" },
]

const imageEditorSchema = generalWidgetEditorSchema.merge(
  z.object({
    content: z.string().min(1, "Image is required"),
    aspectRatio: z.enum(createEnumFromOptions(aspectRatioOptions)).optional(),
    fill: z.enum(createEnumFromOptions(fillOptions)).optional(),
    fullWidth: z.boolean().optional(),
  }),
)

type ImageEditorSchema = z.infer<typeof imageEditorSchema>

export const ImageEditor = ({
  data,
  onUpdate,
}: {
  data: components["schemas"]["ImageWidget"]
  onUpdate: (updatedData: components["schemas"]["ImageWidget"]) => void
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [fileInputError, setFileInputError] = useState<string | null>(null)
  const [previewUrl, setPreviewUrl] = useState<string>("")
  const [initialRender, setInitialRender] = useState(true)
  const form = useForm<ImageEditorSchema>({
    resolver: zodResolver(imageEditorSchema),
    defaultValues: {
      name: "",
      isHidden: false,
      content: "",
      aspectRatio: "1:1",
      fill: "fill",
      fullWidth: false,
    },
  })

  useDeepCompareEffect(() => {
    if (data && initialRender) {
      const content = data.config.content || ""
      form.reset({
        name: data.name || "",
        isHidden: data.isHidden ?? false,
        content: content,
        aspectRatio: data.config.design?.aspectRatio || "1:1",
        fill: data.config.design?.fill || "fill",
        fullWidth: data.config.design?.fullWidth ?? false,
      })
      setPreviewUrl(content)
      setInitialRender(false)
    }
  }, [data, form, initialRender])

  useEffect(() => {
    if (!data) return

    return form.subscribe({
      name: "",
      formState: {
        values: true,
      },
      callback({ values }) {
        const updatedData: components["schemas"]["ImageWidget"] = {
          ...data,
          name: values.name || "",
          isHidden: values.isHidden ?? false,
          config: {
            ...data.config,
            content: values.content || "",
            design: {
              aspectRatio:
                values.aspectRatio as components["schemas"]["ImageWidget"]["config"]["design"]["aspectRatio"],
              fill: values.fill as components["schemas"]["ImageWidget"]["config"]["design"]["fill"],
              fullWidth: values.fullWidth ?? false,
            },
          },
        }
        onUpdate(updatedData)
      },
    })
  }, [data, form, onUpdate])

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFileInputError(null)
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.type.startsWith("image/")) {
      setFileInputError("Please select a valid image file")
      return
    }

    if (file.size > IMAGE_SIZE_LIMIT_IN_MB) {
      setFileInputError("File size must be less than 5MB")
      return
    }

    const reader = new FileReader()
    reader.onload = (e) => {
      const base64String = e.target?.result as string
      form.setValue("content", base64String)
      setPreviewUrl(base64String)
    }
    reader.readAsDataURL(file)
  }

  const handleRemoveImage = () => {
    form.setValue("content", "")
    setPreviewUrl("")
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <Form {...form}>
      <form className="divide-y">
        <GeneralWidgetFields />
        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Content
          </Text>

          <div className="space-y-1">
            <Input ref={fileInputRef} type="file" accept="image/*" onChange={handleFileUpload} />
            {fileInputError && <Hint variant="error">{fileInputError}</Hint>}
          </div>

          {previewUrl && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Text size="small">Preview</Text>
                <button type="button" onClick={handleRemoveImage} className="text-sm text-ui-button-danger">
                  Remove
                </button>
              </div>
              <div className="rounded-lg border bg-ui-bg-subtle p-2">
                <img
                  src={previewUrl}
                  alt="Preview"
                  className="mx-auto max-h-48 max-w-full rounded object-contain"
                  onError={() => {
                    setPreviewUrl("")
                    form.setValue("content", "")
                  }}
                />
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Design
          </Text>
          <div className="space-y-4">
            <Form.Field
              control={form.control}
              name="aspectRatio"
              render={({ field: { ref, onChange, ...field } }) => {
                return (
                  <Form.Item className="flex-row justify-between gap-4">
                    <Form.Label>Aspect Ratio</Form.Label>
                    <Form.Control>
                      <Select {...field} onValueChange={onChange}>
                        <Select.Trigger ref={ref} className="w-[170px]">
                          <Select.Value />
                        </Select.Trigger>
                        <Select.Content>
                          {aspectRatioOptions.map((item) => (
                            <Select.Item key={item.value} value={item.value}>
                              {item.label}
                            </Select.Item>
                          ))}
                        </Select.Content>
                      </Select>
                    </Form.Control>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />
            <Form.Field
              control={form.control}
              name="fill"
              render={({ field: { ref, onChange, ...field } }) => {
                return (
                  <Form.Item className="flex-row justify-between gap-4">
                    <Form.Label>Fill</Form.Label>
                    <Form.Control>
                      <Select {...field} onValueChange={onChange}>
                        <Select.Trigger ref={ref} className="w-[170px]">
                          <Select.Value />
                        </Select.Trigger>
                        <Select.Content>
                          {fillOptions.map((item) => (
                            <Select.Item key={item.value} value={item.value}>
                              {item.label}
                            </Select.Item>
                          ))}
                        </Select.Content>
                      </Select>
                    </Form.Control>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />

            <Form.Field
              control={form.control}
              name="fullWidth"
              render={({ field: { value, onChange, ...field } }) => {
                return (
                  <Form.Item className="flex flex-row items-center gap-3">
                    <Form.Control>
                      <Checkbox checked={value} onCheckedChange={onChange} {...field} />
                    </Form.Control>
                    <Form.Label>Full Width</Form.Label>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />
          </div>
        </div>
      </form>
    </Form>
  )
}
