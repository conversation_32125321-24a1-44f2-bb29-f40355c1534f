/**
 * Extracts a simplified schema representation from JSON response data for field selection.
 *
 * Creates a tree structure where:
 * - Objects become objects with the same keys (expandable nodes)
 * - Arrays become single-element arrays showing item structure
 * - Primitives become empty objects {} (selectable leaf fields)
 *
 * @param data - The JSON response data to extract schema from
 * @returns Schema object where {} indicates selectable fields
 *
 * @example
 * const response = {
 *   users: [
 *     { id: 1, name: "<PERSON>", profile: { email: "<EMAIL>" } }
 *   ],
 *   count: 10,
 *   active: true
 * };
 *
 * const schema = extractResponseSchema(response);
 * // Result:
 * // {
 * //   users: [{
 * //     id: {},           // ✅ Selectable field: users[0].id
 * //     name: {},         // ✅ Selectable field: users[0].name
 * //     profile: {        // 🔽 Expandable node
 * //       email: {}       // ✅ Selectable field: users[0].profile.email
 * //     }
 * //   }],
 * //   count: {},          // ✅ Selectable field: count
 * //   active: {}          // ✅ Selectable field: active
 * // }
 *
 * // Usage in field selector UI:
 * // - Empty objects {} = selectable fields (leaves)
 * // - Non-empty objects = expandable nodes (branches)
 * // - Arrays show structure of items for selection
 *
 * Returns empty object on extraction errors.
 */
export const extractSchema = (data: any): Record<string, any> => {
  try {
    return extractSchemaFromElement(data)
  } catch (error) {
    console.error("Error extracting schema:", error)
    return {}
  }
}

/**
 * Recursively extracts schema from any JSON element
 */
const extractSchemaFromElement = (element: any): any => {
  if (element === null || element === undefined) {
    return {}
  }

  if (Array.isArray(element)) {
    return extractArraySchema(element)
  }

  if (typeof element === "object") {
    return extractObjectSchema(element)
  }

  // Primitive types are selectable fields
  return true
}

/**
 * Extracts schema from object by analyzing all properties
 */
const extractObjectSchema = (element: Record<string, any>): Record<string, any> => {
  const schema: Record<string, any> = {}

  for (const [key, value] of Object.entries(element)) {
    schema[key] = extractSchemaFromElement(value)
  }

  return schema
}

/**
 * Extracts schema from array by analyzing the first element.
 * Assumes all array items have the same structure.
 */
const extractArraySchema = (element: any[]): any[] => {
  if (element.length > 0) {
    const firstItem = element[0]
    return [extractSchemaFromElement(firstItem)]
  }

  return []
}
