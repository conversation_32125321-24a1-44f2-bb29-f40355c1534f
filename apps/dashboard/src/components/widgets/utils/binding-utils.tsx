import { Liquid } from "liquidjs"

const engine = new Liquid()

// Utility function to access nested object properties by string path
export const getNestedProperty = (obj: any, path: string): any => {
  if (!obj || !path) return obj

  return path.split(".").reduce((current, key) => {
    return current && current[key] !== undefined ? current[key] : undefined
  }, obj)
}

// Utility function to render liquid templates with data
export const renderLiquidTemplate = async (template: string, data: any): Promise<string> => {
  try {
    if (!template || typeof template !== "string") {
      return ""
    }

    // If template doesn't contain liquid syntax, return as-is
    if (!template.includes("{{") && !template.includes("{%")) {
      return template
    }

    const result = await engine.parseAndRender(template, data)
    return result || ""
  } catch (error) {
    console.warn("Failed to render template:", template, error)
    return template // Return original template if parsing fails
  }
}
