import { Widget } from "@/routes/mini-apps/mini-app-editor-design/stores/mini-app-design-store"
import { But<PERSON> } from "@medusajs/icons"
import {
  IconAlertCircle,
  IconChartBar,
  IconForms,
  IconLetterCase,
  IconLink,
  IconList,
  IconListDetails,
  IconPhoto,
  IconSpacingVertical,
  IconTextCaption,
  IconTextSize,
  IconVideo,
} from "@tabler/icons-react"
import { ReactNode } from "react"

export type WidgetOption = {
  label: string
  widgets: {
    name: string
    widgetType: Widget["widgetType"]
    icon: ReactNode
    description?: string
  }[]
}
export const widgetOptions: WidgetOption[] = [
  {
    label: "Layout",
    widgets: [
      {
        name: "Title",
        widgetType: "title",
        icon: <IconTextCaption />,
        description: "Display a prominent title with optional subtitle and image",
      },
      {
        name: "Separator",
        widgetType: "separator",
        icon: <IconSpacingVertical />,
        description: "Add space between widgets",
      },
    ],
  },
  {
    label: "Data",
    widgets: [
      {
        name: "Collection",
        widgetType: "collection",
        icon: <IconListDetails />,
        description: "Display a collection of items from a data source",
      },
    ],
  },
  {
    label: "Content",
    widgets: [
      {
        name: "Text",
        widgetType: "text",
        icon: <IconLetterCase />,
        description: "Display text content with alignment options",
      },
      {
        name: "Rich Text",
        widgetType: "rich_text",
        icon: <IconTextSize />,
        description: "Display formatted text with styling options",
      },
      {
        name: "Alert",
        widgetType: "alert",
        icon: <IconAlertCircle />,
        description: "Display an alert message with configurable appearance",
      },
      {
        name: "Fields",
        widgetType: "fields",
        icon: <IconList />,
        description: "Display content in a structured field layout",
      },
      {
        name: "Image",
        widgetType: "image",
        icon: <IconPhoto />,
        description: "Display an image with optional click-to-expand",
      },
      {
        name: "Video",
        widgetType: "video",
        icon: <IconVideo />,
        description: "Display a video with configurable aspect ratio",
      },
      {
        name: "Big Number",
        widgetType: "big_number",
        icon: <IconChartBar />,
        description: "Highlight important numeric data with titles and descriptions",
      },
    ],
  },
  {
    label: "Interactive",
    widgets: [
      {
        name: "Button",
        widgetType: "button",
        icon: <Button />,
        description: "Add a clickable button with configurable styles",
      },
      {
        name: "Link",
        widgetType: "link",
        icon: <IconLink />,
        description: "Add a hyperlink to external content",
      },
    ],
  },
  {
    label: "Forms",
    widgets: [
      {
        name: "Form",
        widgetType: "form",
        icon: <IconForms />,
        description: "Container for grouping form elements",
      },
    ],
  },
]

export const widgetOptionsMap = widgetOptions.reduce(
  (map, group) => {
    group.widgets.forEach((widget) => {
      map[widget.widgetType] = widget
    })
    return map
  },
  {} as Record<Widget["widgetType"], WidgetOption["widgets"][number]>,
)
