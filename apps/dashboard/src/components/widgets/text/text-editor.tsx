import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { Options } from "@/configs"
import { createEnumFromOptions } from "@/lib/conversion"
import { zodResolver } from "@hookform/resolvers/zod"
import { components } from "@saf/sdk"
import { Select, Text, Textarea } from "@saf/ui"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { useDeepCompareEffect } from "use-deep-compare"
import { z } from "zod"
import { generalWidgetEditorSchema, GeneralWidgetFields } from "../general"

const textAlignOptions: Options = [
  {
    label: "Left",
    value: "left",
  },
  {
    label: "Center",
    value: "center",
  },
  {
    label: "Right",
    value: "right",
  },
]

const styleOption: Options = [
  {
    label: "Large",
    value: "large",
  },
  {
    label: "Regular",
    value: "regular",
  },
  {
    label: "Small",
    value: "small",
  },
  {
    label: "Footnote",
    value: "footnote",
  },
  {
    label: "Meta Text",
    value: "meta_text",
  },
  {
    label: "Headline XSmall",
    value: "headline_xsmall",
  },
  {
    label: "Headline Small",
    value: "headline_small",
  },
  {
    label: "Headline Medium",
    value: "headline_medium",
  },
  {
    label: "Headline Large",
    value: "headline_large",
  },
  {
    label: "Headline XLarge",
    value: "headline_xlarge",
  },
]

const textEditorSchema = generalWidgetEditorSchema.merge(
  z.object({
    content: z.string().optional(),
    style: z.enum(createEnumFromOptions(styleOption)),
    textAlign: z.string().optional(),
  }),
)

type TextEditorSchema = z.infer<typeof textEditorSchema>

export const TextEditor = ({
  data,
  onUpdate,
}: {
  data: components["schemas"]["TextWidget"]
  onUpdate: (updatedData: components["schemas"]["TextWidget"]) => void
}) => {
  const [initialRender, setInitialRender] = useState(true)
  const form = useForm<TextEditorSchema>({
    resolver: zodResolver(textEditorSchema),
    defaultValues: {
      name: "",
      isHidden: false,
      content: "",
      style: "regular",
      textAlign: "left",
    },
  })

  useDeepCompareEffect(() => {
    if (data && initialRender) {
      form.reset({
        name: data.name || "",
        isHidden: data.isHidden ?? false,
        content: data.config.content || "",
        style: data.config.design?.style || "regular",
        textAlign: data.config.design?.textAlign || "left",
      })
      setInitialRender(false)
    }
  }, [data, form, initialRender])

  useEffect(() => {
    if (!data) return

    return form.subscribe({
      name: "",
      formState: {
        values: true,
      },
      callback({ values }) {
        const updatedData: components["schemas"]["TextWidget"] = {
          ...data,
          name: values.name || "",
          isHidden: values.isHidden ?? false,
          config: {
            ...data.config,
            content: values.content || "",
            design: {
              // TODO: revise this for better type safety
              style: values.style as components["schemas"]["TextWidget"]["config"]["design"]["style"],
              textAlign: values.textAlign as components["schemas"]["TextWidget"]["config"]["design"]["textAlign"],
            },
          },
        }
        onUpdate(updatedData)
      },
    })
  }, [data, form, onUpdate])

  return (
    <Form {...form}>
      <form className="divide-y">
        <GeneralWidgetFields />
        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Content
          </Text>
          <Field control={form.control} name="content">
            <Textarea className="min-h-36" />
          </Field>
        </div>
        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Design
          </Text>
          <div className="space-y-4">
            <Form.Field
              control={form.control}
              name="style"
              render={({ field: { ref, onChange, ...field } }) => {
                return (
                  <Form.Item className="flex-row justify-between gap-4">
                    <Form.Label>Style</Form.Label>
                    <Form.Control>
                      <Select {...field} onValueChange={onChange}>
                        <Select.Trigger ref={ref} className="w-[170px]">
                          <Select.Value />
                        </Select.Trigger>
                        <Select.Content>
                          {styleOption.map((item) => (
                            <Select.Item key={item.value} value={item.value}>
                              {item.label}
                            </Select.Item>
                          ))}
                        </Select.Content>
                      </Select>
                    </Form.Control>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />
            <Form.Field
              control={form.control}
              name="textAlign"
              render={({ field: { ref, onChange, ...field } }) => {
                return (
                  <Form.Item className="flex-row justify-between gap-4">
                    <Form.Label>Alignment</Form.Label>
                    <Form.Control>
                      <Select {...field} onValueChange={onChange}>
                        <Select.Trigger ref={ref} className="w-[170px]">
                          <Select.Value />
                        </Select.Trigger>
                        <Select.Content>
                          {textAlignOptions.map((item) => (
                            <Select.Item key={item.value} value={item.value}>
                              {item.label}
                            </Select.Item>
                          ))}
                        </Select.Content>
                      </Select>
                    </Form.Control>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />
          </div>
        </div>
      </form>
    </Form>
  )
}
