import { cn } from "@/lib/utils"
import { useUpdateWidget } from "@/routes/mini-apps/mini-app-editor-design/hooks/use-widgets"
import { Widget } from "@/routes/mini-apps/mini-app-editor-design/stores/mini-app-design-store"
import { components, operations } from "@saf/sdk"
import { toast } from "@saf/ui"
import { useCallback } from "react"
import { useDebouncedCallback } from "use-debounce"
import { AlertEditor, AlertWidget } from "./alert"
import { BigNumberEditor, BigNumberWidget } from "./big-number"
import { ButtonEditor, ButtonWidget } from "./button"
import { CollectionEditor, CollectionWidget } from "./collection"
import { FieldsEditor, FieldsWidget } from "./fields"
import { FormEditor, FormWidget } from "./form"
import { ImageEditor, ImageWidget } from "./image"
import { SeparatorEditor, SeparatorWidget } from "./separator"
import { TextEditor, TextWidget } from "./text"
import { TitleEditor, TitleWidget } from "./title"
import { VideoEditor, VideoWidget } from "./video"

export * from "./alert"
export * from "./button"
export * from "./collection"
export * from "./form"
export * from "./image"
export * from "./separator"
export * from "./text"
export * from "./title"

type UpdateRequestBody = operations["MiniAppWidget_update"]["requestBody"]["content"]["application/json"]

export const WidgetEditor = ({
  teamId,
  miniAppId,
  versionId,
  pageId,
  widget,
}: {
  teamId: string
  miniAppId: string
  versionId: string
  pageId: string
  widget: Widget
}) => {
  const updateWidget = useUpdateWidget()

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedOnUpdate = useCallback(
    useDebouncedCallback((values: UpdateRequestBody) => {
      updateWidget.mutate(
        {
          body: values,
          params: {
            path: {
              teamId: parseInt(teamId),
              miniAppId: parseInt(miniAppId),
              versionId: parseInt(versionId),
              pageId: parseInt(pageId),
              widgetId: widget.id,
            },
          },
        },
        {
          onError: (error) => {
            toast.error(error.message || "Failed to update widget")
          },
        },
      )
    }, 500),
    [teamId, miniAppId, versionId, pageId, widget.id],
  )

  switch (widget.widgetType) {
    case "collection":
      return (
        <CollectionEditor data={widget as components["schemas"]["CollectionWidget"]} onUpdate={debouncedOnUpdate} />
      )
    case "text":
      return <TextEditor data={widget as components["schemas"]["TextWidget"]} onUpdate={debouncedOnUpdate} />
    case "title":
      return <TitleEditor data={widget as components["schemas"]["TitleWidget"]} onUpdate={debouncedOnUpdate} />
    case "alert":
      return <AlertEditor data={widget as components["schemas"]["AlertWidget"]} onUpdate={debouncedOnUpdate} />
    case "button":
      return <ButtonEditor data={widget as components["schemas"]["ButtonWidget"]} onUpdate={debouncedOnUpdate} />
    case "separator":
      return <SeparatorEditor data={widget as components["schemas"]["SeparatorWidget"]} onUpdate={debouncedOnUpdate} />
    case "image":
      return <ImageEditor data={widget as components["schemas"]["ImageWidget"]} onUpdate={debouncedOnUpdate} />
    case "video":
      return <VideoEditor data={widget as components["schemas"]["VideoWidget"]} onUpdate={debouncedOnUpdate} />
    case "fields":
      return <FieldsEditor data={widget as components["schemas"]["FieldsWidget"]} onUpdate={debouncedOnUpdate} />
    case "big_number":
      return <BigNumberEditor data={widget as components["schemas"]["BigNumberWidget"]} onUpdate={debouncedOnUpdate} />
    case "form":
      return <FormEditor data={widget as components["schemas"]["FormWidget"]} onUpdate={debouncedOnUpdate} />
    default:
      return <div>{widget.widgetType}</div>
  }
}

export const renderWidget = (
  widget: Widget,
  focusedWidgetId?: number,
  onWidgetChanged?: (widgetId: number) => void,
) => {
  let widgetToReturn: React.ReactNode

  switch (widget.widgetType) {
    case "collection":
      widgetToReturn = <CollectionWidget data={widget as components["schemas"]["CollectionWidget"]} />
      break
    case "text":
      widgetToReturn = <TextWidget data={widget as components["schemas"]["TextWidget"]} />
      break
    case "title":
      widgetToReturn = <TitleWidget data={widget as components["schemas"]["TitleWidget"]} />
      break
    case "alert":
      widgetToReturn = <AlertWidget data={widget as components["schemas"]["AlertWidget"]} />
      break
    case "button":
      widgetToReturn = <ButtonWidget data={widget as components["schemas"]["ButtonWidget"]} />
      break
    case "separator":
      widgetToReturn = <SeparatorWidget data={widget as components["schemas"]["SeparatorWidget"]} />
      break
    case "image":
      widgetToReturn = <ImageWidget data={widget as components["schemas"]["ImageWidget"]} />
      break
    case "form":
      widgetToReturn = <FormWidget data={widget as components["schemas"]["FormWidget"]} />
      break
    case "video":
      widgetToReturn = <VideoWidget data={widget as components["schemas"]["VideoWidget"]} />
      break
    case "fields":
      widgetToReturn = <FieldsWidget data={widget as components["schemas"]["FieldsWidget"]} />
      break
    case "big_number":
      widgetToReturn = <BigNumberWidget data={widget as components["schemas"]["BigNumberWidget"]} />
      break
    default:
      widgetToReturn = (
        <div className="mx-4 my-2 rounded border bg-ui-tag-orange-bg p-2 text-sm">{widget.widgetType}</div>
      )
      break
  }

  return (
    <div key={widget.id} onClick={() => onWidgetChanged?.(widget.id)} className="relative">
      <div
        className={cn(
          widget.id === focusedWidgetId && "pointer-events-none absolute inset-0 border-2 border-ui-bg-interactive",
        )}
      />
      {widgetToReturn}
    </div>
  )
}
