import { cn } from "@/lib/utils"
import { components } from "@saf/sdk"
import { cva } from "class-variance-authority"

const containerVariants = cva("rounded-md border border-ui-border-base bg-ui-bg-base", {
  variants: {
    size: {
      small: "p-2",
      medium: "p-2.5",
      large: "p-3",
    },
  },
  defaultVariants: {
    size: "medium",
  },
})

const titleVariants = cva("text-ui-fg-subtle", {
  variants: {
    size: {
      small: "text-xs",
      medium: "text-sm",
      large: "text-base",
    },
  },
  defaultVariants: {
    size: "medium",
  },
})

const valueVariants = cva("font-medium", {
  variants: {
    size: {
      small: "text-base",
      medium: "text-lg",
      large: "text-2xl",
    },
  },
  defaultVariants: {
    size: "medium",
  },
})

export const BigNumberWidget = ({ data }: { data: components["schemas"]["BigNumberWidget"] }) => {
  const fields = data.config.fields || []
  const size = data.config.design?.size || "medium"

  if (fields.length === 0) {
    return (
      <div className="px-3 py-2">
        <div className="text-sm italic text-ui-fg-subtle">No values configured</div>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-2 gap-2 px-3 py-2">
      {fields.map((field, index) => (
        <div
          key={`${field.title}-${field.value}-${index}`}
          className={cn(
            containerVariants({
              size,
            }),
          )}
        >
          <h4
            className={titleVariants({
              size,
            })}
          >
            {field.title}
          </h4>
          <p
            className={valueVariants({
              size,
            })}
          >
            {field.value}
          </p>
        </div>
      ))}
    </div>
  )
}
