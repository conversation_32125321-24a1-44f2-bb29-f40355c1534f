import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { Options } from "@/configs"
import { createEnumFromOptions } from "@/lib/conversion"
import { zodResolver } from "@hookform/resolvers/zod"
import { components } from "@saf/sdk"
import { Button, IconButton, Input, Select, Text } from "@saf/ui"
import { Trash } from "@medusajs/icons"
import { useEffect, useState } from "react"
import { useFieldArray, useForm } from "react-hook-form"
import { useDeepCompareEffect } from "use-deep-compare"
import { z } from "zod"
import { generalWidgetEditorSchema, GeneralWidgetFields } from "../general"

const sizeOption: Options = [
  {
    label: "Small",
    value: "small",
  },
  {
    label: "Medium",
    value: "medium",
  },
  {
    label: "Large",
    value: "large",
  },
]

const bigNumberEditorSchema = generalWidgetEditorSchema.merge(
  z.object({
    fields: z.array(
      z.object({
        title: z.string(),
        value: z.string(),
      }),
    ),
    size: z.enum(createEnumFromOptions(sizeOption)),
  }),
)

type BigNumberEditorSchema = z.infer<typeof bigNumberEditorSchema>

export const BigNumberEditor = ({
  data,
  onUpdate,
}: {
  data: components["schemas"]["BigNumberWidget"]
  onUpdate: (updatedData: components["schemas"]["BigNumberWidget"]) => void
}) => {
  const defaultFields = [{ title: "", value: "" }]
  const [initialRender, setInitialRender] = useState(true)
  const form = useForm<BigNumberEditorSchema>({
    resolver: zodResolver(bigNumberEditorSchema),
    defaultValues: {
      name: "",
      isHidden: false,
      fields: defaultFields,
      size: "medium",
    },
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "fields",
  })

  useDeepCompareEffect(() => {
    if (data && initialRender) {
      const fields = data.config.fields || defaultFields
      form.reset({
        name: data.name || "",
        isHidden: data.isHidden ?? false,
        fields: fields,
        size: data.config.design?.size ?? "medium",
      })
      setInitialRender(false)
    }
  }, [data, form, initialRender])

  useEffect(() => {
    if (!data) return

    return form.subscribe({
      name: "",
      formState: {
        values: true,
      },
      callback({ values }) {
        const updatedData: components["schemas"]["BigNumberWidget"] = {
          ...data,
          name: values.name || "",
          isHidden: values.isHidden ?? false,
          config: {
            ...data.config,
            fields: values.fields || "",
            design: {
              size: values.size as "small" | "medium" | "large",
            },
          },
        }
        onUpdate(updatedData)
      },
    })
  }, [data, form, onUpdate])

  return (
    <Form {...form}>
      <form className="divide-y">
        <GeneralWidgetFields />
        <div className="flex flex-col gap-4 p-4">
          <div className="flex items-center justify-between">
            <Text className="uppercase" size="xsmall" weight="plus">
              Content
            </Text>
            <Button type="button" variant="secondary" size="small" onClick={() => append({ title: "", value: "" })}>
              + Add Item
            </Button>
          </div>

          {fields.map((field, index) => (
            <div key={field.id} className="flex flex-row items-center space-x-2">
              <Field control={form.control} name={`fields.${index}.title`}>
                <Input placeholder="Title" />
              </Field>
              <Field control={form.control} name={`fields.${index}.value`}>
                <Input placeholder="Value" />
              </Field>
              <IconButton type="button" variant="transparent" onClick={() => remove(index)}>
                <Trash className="h-4 w-4" />
              </IconButton>
            </div>
          ))}
        </div>
        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Design
          </Text>
          <div className="space-y-4">
            <Form.Field
              control={form.control}
              name="size"
              render={({ field: { ref, onChange, ...field } }) => {
                return (
                  <Form.Item className="flex-row justify-between gap-4">
                    <Form.Label>Style</Form.Label>
                    <Form.Control>
                      <Select {...field} onValueChange={onChange}>
                        <Select.Trigger ref={ref} className="w-[170px]">
                          <Select.Value />
                        </Select.Trigger>
                        <Select.Content>
                          {sizeOption.map((item) => (
                            <Select.Item key={item.value} value={item.value}>
                              {item.label}
                            </Select.Item>
                          ))}
                        </Select.Content>
                      </Select>
                    </Form.Control>
                    <Form.ErrorMessage />
                  </Form.Item>
                )
              }}
            />
          </div>
        </div>
      </form>
    </Form>
  )
}
