import { Form } from "@/components/common/form"
import { Input, Switch } from "@saf/ui"
import { useFormContext } from "react-hook-form"
import { z } from "zod"

export const generalWidgetEditorSchema = z.object({
  name: z.string().optional(),
  isHidden: z.boolean(),
})

export type GeneralWidgetEditorSchema = z.infer<typeof generalWidgetEditorSchema>

export const GeneralWidgetFields = () => {
  const form = useFormContext<GeneralWidgetEditorSchema>()

  return (
    <div className="space-y-3 p-4">
      <Form.Field
        control={form.control}
        name="name"
        render={({ field }) => (
          <Form.Item>
            <Form.Label inline>
              Name
              <Form.Control>
                <Input {...field} />
              </Form.Control>
            </Form.Label>
            <Form.ErrorMessage />
          </Form.Item>
        )}
      />
      <Form.Field
        control={form.control}
        name="isHidden"
        render={({ field: { value, onChange, ...field } }) => {
          return (
            <Form.Item className="flex flex-row items-center justify-between gap-3">
              <Form.Label>Hide Widget</Form.Label>
              <Form.Control>
                <Switch checked={value} onCheckedChange={onChange} {...field} />
              </Form.Control>
              <Form.ErrorMessage />
            </Form.Item>
          )
        }}
      />
    </div>
  )
}
