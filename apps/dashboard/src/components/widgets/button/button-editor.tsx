import { Field } from "@/components/common/field"
import { Form } from "@/components/common/form"
import { Options } from "@/configs"
import { createEnumFromOptions } from "@/lib/conversion"
import { zodResolver } from "@hookform/resolvers/zod"
import { Plus, Trash } from "@medusajs/icons"
import { components } from "@saf/sdk"
import { Button, IconButton, Input, Select, Text } from "@saf/ui"
import { useEffect, useState } from "react"
import { useFieldArray, useForm } from "react-hook-form"
import { z } from "zod"
import { generalWidgetEditorSchema, GeneralWidgetFields } from "../general"
import { useDeepCompareEffect } from "use-deep-compare"

const actionTypeOptions: Options = [
  {
    label: "Navigate to Page",
    value: "navigate_to_page",
  },
  {
    label: "Navigate Back",
    value: "navigate_back",
  },
  {
    label: "Open URL",
    value: "open_url",
  },
  {
    label: "Submit Form",
    value: "submit_form",
  },
]

const primaryOptions: Options = [
  {
    label: "None",
    value: "none",
  },
  {
    label: "Left",
    value: "left",
  },
  {
    label: "Right",
    value: "right",
  },
]

const buttonActionSchema = z.object({
  title: z.string().min(1, "Title is required"),
  icon: z.string().optional(),
  actionType: z.enum(createEnumFromOptions(actionTypeOptions)),
})

const buttonEditorSchema = generalWidgetEditorSchema.merge(
  z.object({
    buttons: z.array(buttonActionSchema).min(1, "At least one button is required"),
    primary: z.enum(createEnumFromOptions(primaryOptions)).optional(),
  }),
)

type ButtonEditorSchema = z.infer<typeof buttonEditorSchema>

export const ButtonEditor = ({
  data,
  onUpdate,
}: {
  data: components["schemas"]["ButtonWidget"]
  onUpdate: (updatedData: components["schemas"]["ButtonWidget"]) => void
}) => {
  const [initialRender, setInitialRender] = useState(true)
  const form = useForm<ButtonEditorSchema>({
    resolver: zodResolver(buttonEditorSchema),
    defaultValues: {
      name: "",
      isHidden: false,
      buttons: [
        {
          title: "",
          icon: "",
          actionType: "",
        },
      ],
      primary: "none",
    },
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "buttons",
  })

  useDeepCompareEffect(() => {
    if (data && initialRender) {
      form.reset({
        name: data.name || "",
        isHidden: data.isHidden ?? false,
        buttons:
          data.config.buttons?.length > 0
            ? data.config.buttons.map((button) => ({
                title: button.title || "",
                icon: button.icon || "",
                actionType: button.actionType,
              }))
            : [
                {
                  title: "",
                  icon: "",
                  actionType: "",
                },
              ],
        primary: data.config.design?.primary || "none",
      })
      setInitialRender(false)
    }
  }, [data, form, initialRender])

  useEffect(() => {
    if (!data) return

    return form.subscribe({
      name: "",
      formState: {
        values: true,
      },
      callback: ({ values }) => {
        console.log("values", values)
        const updatedData: components["schemas"]["ButtonWidget"] = {
          ...data,
          name: values.name || "",
          isHidden: values.isHidden ?? false,
          config: {
            ...data.config,
            buttons: (values.buttons || []).map((button) => ({
              title: button?.title || "",
              icon: button?.icon || "",
              actionType: (button?.actionType || "navigate_to_page") as components["schemas"]["ActionType"],
            })),
            design: {
              primary: (values.primary || "none") as "left" | "none" | "right",
            },
          },
        }
        onUpdate(updatedData)
      },
    })
  }, [data, form, onUpdate])

  const addButton = () => {
    append({
      title: "",
      icon: "",
      actionType: "navigate_to_page",
    })
  }

  return (
    <Form {...form}>
      <form className="divide-y">
        <GeneralWidgetFields />

        <div className="flex flex-col gap-4 p-4">
          <div className="flex items-center justify-between">
            <Text className="uppercase" size="xsmall" weight="plus">
              Buttons
            </Text>
            <Button
              type="button"
              variant="secondary"
              size="small"
              onClick={addButton}
              className="flex items-center gap-1"
            >
              <Plus className="h-4 w-4" />
              Add Button
            </Button>
          </div>

          <div className="space-y-3">
            {fields.map((field, index) => (
              <div key={field.id} className="space-y-3 rounded-md border border-ui-border-base p-3">
                <div className="flex items-center justify-between">
                  <Text size="small" weight="plus">
                    Button {index + 1}
                  </Text>
                  {fields.length > 1 && (
                    <IconButton type="button" variant="transparent" size="small" onClick={() => remove(index)}>
                      <Trash className="h-4 w-4" />
                    </IconButton>
                  )}
                </div>

                <div className="grid grid-cols-1 gap-3">
                  <Field control={form.control} name={`buttons.${index}.title`} label="Title">
                    <Input placeholder="Button title" />
                  </Field>

                  <Form.Field
                    control={form.control}
                    name={`buttons.${index}.actionType`}
                    render={({ field: { ref, onChange, ...field } }) => {
                      return (
                        <Form.Item className="space-y-2">
                          <Form.Label>Action Type</Form.Label>
                          <Form.Control>
                            <Select {...field} onValueChange={onChange}>
                              <Select.Trigger ref={ref}>
                                <Select.Value />
                              </Select.Trigger>
                              <Select.Content>
                                {actionTypeOptions.map((option) => (
                                  <Select.Item key={option.value} value={option.value}>
                                    {option.label}
                                  </Select.Item>
                                ))}
                              </Select.Content>
                            </Select>
                          </Form.Control>
                          <Form.ErrorMessage />
                        </Form.Item>
                      )
                    }}
                  />

                  <Field control={form.control} name={`buttons.${index}.icon`} label="Icon (optional)">
                    <Input placeholder="Icon name" />
                  </Field>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="flex flex-col gap-4 p-4">
          <Text className="uppercase" size="xsmall" weight="plus">
            Design
          </Text>
          <Form.Field
            control={form.control}
            name="primary"
            render={({ field: { ref, onChange, ...field } }) => {
              return (
                <Form.Item className="flex-row justify-between gap-4">
                  <Form.Label>Primary Button</Form.Label>
                  <Form.Control>
                    <Select {...field} onValueChange={onChange}>
                      <Select.Trigger ref={ref} className="w-[170px]">
                        <Select.Value />
                      </Select.Trigger>
                      <Select.Content>
                        {primaryOptions.map((option) => (
                          <Select.Item key={option.value} value={option.value}>
                            {option.label}
                          </Select.Item>
                        ))}
                      </Select.Content>
                    </Select>
                  </Form.Control>
                  <Form.ErrorMessage />
                </Form.Item>
              )
            }}
          />
        </div>
      </form>
    </Form>
  )
}
