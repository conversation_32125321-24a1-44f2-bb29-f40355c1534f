model FormWidget extends BaseWidget {
  widgetType: WidgetType.form;
  config: {
    title?: string;
    subtitle?: string;
    bindingConfig?: BindingConfig;
    fields: FormFields[];
    design?: {
      submitButtonText?: string;
    };
  };
}

alias FormFields =
  | TextField
  | LongTextField
  | DateTimeField
  | DateField
  | TimeField
  | CurrencyField
  | NumberField
  | PhoneField
  | EmailField
  | CheckboxField
  | ImagePickerField
  | FilePickerField
  | DropdownField
  | RadioField;

model BaseFormField {
  id: string;
  isHidden: boolean;
  label: string;
  required: boolean;
  hint?: string;
  binding?: string;
}

model TextField extends BaseFormField {
  type: "text";
  validation?: {
    minLength?: numeric;
    maxLength?: numeric;
  };
}

model LongTextField extends BaseFormField {
  type: "long_text";
  validation?: {
    minLength?: numeric;
    maxLength?: numeric;
  };
}

model DateTimeField extends BaseFormField {
  type: "date_time";
  validation?: {
    minDate?: string;
    maxDate?: string;
  };
}

model DateField extends BaseFormField {
  type: "date";
  validation?: {
    minDate?: string;
    maxDate?: string;
  };
}

model TimeField extends BaseFormField {
  type: "time";
  validation?: {
    minTime?: string;
    maxTime?: string;
  };
}

model CurrencyField extends BaseFormField {
  type: "currency";
  validation?: {
    min?: numeric;
    max?: numeric;
  };
}

model NumberField extends BaseFormField {
  type: "number";
  validation?: {
    min?: numeric;
    max?: numeric;
    isInteger?: boolean;
  };
}

model PhoneField extends BaseFormField {
  type: "phone";
}

model EmailField extends BaseFormField {
  type: "email";
}

model CheckboxField extends BaseFormField {
  type: "checkbox";
  defaultValue?: boolean;
}

model ImagePickerField extends BaseFormField {
  type: "image_picker";
  allowMultiple?: boolean;
}

model FilePickerField extends BaseFormField {
  type: "file_picker";
  allowMultiple?: boolean;
}

model DropdownField extends BaseFormField {
  type: "dropdown";
  options?: {
    label: string;
    value: string;
  }[];
  allowMultiple?: boolean;
}

model RadioField extends BaseFormField {
  type: "radio";
  options?: {
    label: string;
    value: string;
  }[];
}
