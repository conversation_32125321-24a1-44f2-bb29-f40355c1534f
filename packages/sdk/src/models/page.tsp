using Http;

model Page {
  id: int32;
  title: string;
  content: string;
  status: string;
  createdAt: utcDateTime;
  createdBy: string;
  updatedAt?: utcDateTime;
  updatedBy?: string;
  publishedAt?: utcDateTime;
}

alias PageResponse = Page;
model CreatePageResult
  is OmitProperties<
    Page,

      | "id"
      | "status"
      | "createdAt"
      | "createdBy"
      | "updatedAt"
      | "updatedBy"
      | "publishedAt"
  >;
model UpdatePageRequest
  is OmitProperties<
    Page,
    "id" | "createdAt" | "createdBy" | "updatedAt" | "updatedBy"
  >;
model PagePaginationParam extends PaginationParams {
  @query
  title?: string;

  @query
  status?: string;
}

model MiniAppPage {
  id: int32;
  miniAppVersionId: int32;
  name: string;
  title?: string;
  config?: MiniAppPageConfig;
  icon?: string;
  isHidden: boolean;
  hideInNavbar: boolean;
  position: int32;
  widgets?: Widgets[];
}

model MiniAppPageConfig {
  variables: Record<unknown>;
}

model CreateMiniAppPageRequest
  is OmitProperties<
    MiniAppPage,

      | "id"
      | "miniAppVersionId"
      | "createdAt"
      | "createdBy"
      | "updatedAt"
      | "updatedBy"
  >;

model UpdateMiniAppPageRequest
  is OmitProperties<
    MiniAppPage,

      | "id"
      | "miniAppVersionId"
      | "createdAt"
      | "createdBy"
      | "updatedAt"
      | "updatedBy"
  >;

alias MiniAppPageResponse = MiniAppPage;
