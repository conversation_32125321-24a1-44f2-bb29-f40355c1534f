model ExternalApi {
  id: int32;
  name: string;
  baseUrl?: string;
  urlPath: string;
  httpMethod:
    | "get"
    | "post"
    | "put"
    | "delete"
    | "patch"
    | "options"
    | "head"
    | "connect"
    | "trace";
  config: ExternalApiRequestConfig;
  savedResponse?: unknown;
}

model ExternalApiResponse is ExternalApi;

model CreateExternalApiRequest is OmitProperties<ExternalApi, "id">;

model UpdateExternalApiRequest is OmitProperties<ExternalApi, "id">;

model ExternalApiResponseSnapshot {
  isSuccess: boolean;
  statusCode: int32;
  data: unknown;
  schema: unknown;
}
