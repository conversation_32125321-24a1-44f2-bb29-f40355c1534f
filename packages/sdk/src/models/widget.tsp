model CreateMiniAppWidgetRequest {
  ...OmitProperties<BaseWidget, "id">;
  widgetType: WidgetType;
  config: unknown;
}

alias UpdateMiniAppWidgetRequest = Widgets;

alias MiniAppWidgetResponse = Widgets;

enum WidgetType {
  collection: "collection",
  title: "title",
  separator: "separator",
  text: "text",
  richText: "rich_text",
  alert: "alert",
  fields: "fields",
  image: "image",
  video: "video",
  bigNumber: "big_number",
  button: "button",
  link: "link",
  form: "form",
}

alias Widgets =
  | CollectionWidget
  | SeparatorWidget
  | TitleWidget
  | TextWidget
  | RichTextWidget
  | AlertWidget
  | FieldsWidget
  | ImageWidget
  | VideoWidget
  | BigNumberWidget
  | ButtonWidget
  | LinkWidget
  | FormWidget;

// Primary Data Widgets
model CollectionWidget extends BaseWidget {
  widgetType: WidgetType.collection;
  config: {
    bindingConfig?: BindingConfig;
    rootArrayPath?: string;
    style: "list" | "grid";
    itemsData: {
      title: string;
      subtitle: string;
      meta: string;
      image: string;
    };
    design: {
      size: "default" | "compact";
      style: "default" | "card";
      imageShape: "circle" | "square";
    };
    options: {
      limitItems?: int32;
    };
    pagination: {
      enabled: boolean;
      requestPage: string;
      requestLimit: string;
      size?: numeric;
    };
  };
}

// Layout Widgets
model SeparatorWidget extends BaseWidget {
  widgetType: WidgetType.separator;
  config: {
    design: {
      size: "small" | "medium" | "large";
      drawLine?: boolean;
    };
  };
}

// Content Widgets
model TitleWidget extends BaseWidget {
  widgetType: WidgetType.title;
  config: {
    style: "simple" | "banner";
    data: {
      title?: string;
      subtitle?: string;
      meta?: string;
      image?: string;
    };
    design: {
      imageFill: "fill" | "contain";
    };
  };
}

model TextWidget extends BaseWidget {
  widgetType: WidgetType.text;
  config: {
    content: string;
    design: {
      style:
        | "large"
        | "regular"
        | "small"
        | "footnote"
        | "meta_text"
        | "headline_xsmall"
        | "headline_small"
        | "headline_medium"
        | "headline_large"
        | "headline_xlarge";
      textAlign: "left" | "center" | "right";
    };
  };
}

model RichTextWidget extends BaseWidget {
  widgetType: WidgetType.richText;
  config: {
    content: string;
  };
}

model AlertWidget extends BaseWidget {
  widgetType: WidgetType.alert;
  config: {
    title: string;
    subtitle?: string;
    design: {
      type: "info" | "warning" | "error" | "success";
    };
  };
}

model FieldsWidget extends BaseWidget {
  widgetType: WidgetType.fields;
  config: {
    fields: {
      label: string;
      value: string;
    }[];
    design: {
      style: "default" | "compact";
    };
  };
}

model ImageWidget extends BaseWidget {
  widgetType: WidgetType.image;
  config: {
    content: string;
    design: {
      aspectRatio: "1:1" | "16:9" | "4:3" | "3:2" | "auto";
      fill: "fill" | "contain";
      fullWidth?: boolean;
    };
  };
}

model VideoWidget extends BaseWidget {
  widgetType: WidgetType.video;
  config: {
    url: string;
    design?: {
      aspectRatio: "1:1" | "16:9" | "4:3" | "3:2";
      fullWidth?: boolean;
    };
  };
}

model BigNumberWidget extends BaseWidget {
  widgetType: WidgetType.bigNumber;
  config: {
    fields: {
      title: string;
      value?: string;
    }[];
    design?: {
      size?: "small" | "medium" | "large";
    };
  };
}

// Interactive Widgets
model ButtonWidget extends BaseWidget {
  widgetType: WidgetType.button;
  config: {
    buttons: Action[];
    design?: {
      primary?: "left" | "none" | "right";
    };
  };
}

model LinkWidget extends BaseWidget {
  widgetType: WidgetType.link;
  config: {
    links: Action[];
    design?: {
      size?: "left" | "none" | "right";
    };
  };
}

// Base Widget
model BaseWidget {
  id: int32;
  name: string;
  position: int32;
  isHidden: boolean;
}

model BindingConfig {
  dataSourceId?: numeric;
  externalApiId?: numeric;
}

// Actions Related
model Action {
  title: string;
  icon: string;
  actionType: ActionType;
}

enum ActionType {
  navigateToPage: "navigate_to_page",
  navigateBack: "navigate_back",
  openUrl: "open_url",
  submitForm: "submit_form",
}
