import "@typespec/http";

using Http;

@tag("Admin - Post Categories")
@route("/post-categories")
namespace SAF.Admin.PostCategory;

@get
op listPostCategories(...PostCategoryPaginationParam): {
  @statusCode status: 200;
  @body response: PagedResult<PostCategoryResponse>;
} | AllErrorResponse;

@post
op createPostCategory(
  @header contentType: "multipart/form-data", 
  @multipartBody postCategory:  {
    name: HttpPart<string>,
    position?: HttpPart<integer>,
    isEnabled?: HttpPart<boolean>,
    image?: HttpPart<File>,
  }
): {
  @statusCode statusCode: 200;
  @body createdPostCategory: PostCategoryResponse;
} | AllErrorResponse;

@patch(#{ implicitOptionality: true })
op updatePostCategory(
  @path postCategoryId: int32,
  @header contentType: "multipart/form-data", 
  @multipartBody postCategory:  {
    name: HttpPart<string>,
    isEnabled?: HttpPart<boolean>,
    image?: HttpPart<File>,
  }
): {
  @statusCode statusCode: 200;
  @body updatedPostCategory: PostCategoryResponse;
} | AllErrorResponse;

@delete
op deletePostCategory(@path postCategoryId: int32): {
  @statusCode statusCode: 204;
} | AllErrorResponse;

@patch(#{ implicitOptionality: true })
@route("/{postCategoryId}/move")
op movePostCategory(@path postCategoryId: int32, @body request: MoveRequest): {
  @statusCode statusCode: 204;
} | AllErrorResponse;
