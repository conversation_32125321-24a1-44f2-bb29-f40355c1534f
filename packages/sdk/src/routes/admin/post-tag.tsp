import "@typespec/http";

using Http;

@tag("Admin - Post Tags")
@route("/post-tags")
namespace SAF.Admin.PostTag;

@get
op listPostTags(@query name?: string, ...PaginationParams): {
  @statusCode status: 200;
  @body response: PagedResult<PostTagResponse>;
} | AllErrorResponse;

@post
op createPostTag(@body postTag: CreateUpdatePostTagRequest): {
  @statusCode statusCode: 200;
  @body createdPostTag: PostTagResponse;
} | AllErrorResponse;

@patch(#{ implicitOptionality: true })
op updatePostTag(@path postTagId: int32, @body postTag: CreateUpdatePostTagRequest): {
  @statusCode statusCode: 200;
  @body updatedPost: PostTagResponse;
} | AllErrorResponse;

@delete
op deletePostTag(@path postTagId: int32): {
  @statusCode statusCode: 204;
} | AllErrorResponse;

@patch(#{ implicitOptionality: true })
@route("/{postTagId}/move")
op movePostTag(@path postTagId: int32,  @body request: MoveRequest): {
  @statusCode statusCode: 204;
} | AllErrorResponse;