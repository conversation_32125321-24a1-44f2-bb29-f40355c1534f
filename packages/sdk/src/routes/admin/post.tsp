import "@typespec/http";

using Http;

@tag("Admin - Posts")
@route("/posts")
namespace SAF.Admin.Post;

@get
op listPosts(...PaginationParams): {
  @statusCode status: 200;
  @body response: PagedResult<PostResponse>;
} | AllErrorResponse;

@get
op getPost(@path postId: int32): {
  @statusCode statusCode: 200;
  @body post: PostResponse;
} | AllErrorResponse;

@post
op createPost(
  @header contentType: "multipart/form-data", 
  @multipartBody post:  {
    title: HttpPart<string>,
    excerpt: HttpPart<string>,
    content?: HttpPart<string>,
    image?: HttpPart<File>,
  }
): {
  @statusCode statusCode: 200;
  @body createdPost: PostResponse;
} | AllErrorResponse;

@patch(#{ implicitOptionality: true })
op updatePost(
  @path postId: int32,
  @header contentType: "multipart/form-data", 
  @multipartBody post:  {
    title: HttpPart<string>,
    excerpt: HttpPart<string>,
    content?: HttpPart<string>,
    image?: HttpPart<File>,
  }
): {
  @statusCode statusCode: 200;
  @body updatedPost: PostResponse;
} | AllErrorResponse;

@delete
op deletePost(@path postId: int32): {
  @statusCode statusCode: 204;
} | AllErrorResponse;
