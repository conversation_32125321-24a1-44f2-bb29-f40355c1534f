import "@typespec/http";

using Http;

@tag("Admin - Pages")
@route("/pages")
namespace SAF.Admin.Page;

@get
op listPages(...PaginationParams): {
  @statusCode status: 200;
  @body response: PagedResult<PageResponse>;
} | AllErrorResponse;

@get
op getPage(@path pageId: int32): {
  @statusCode statusCode: 200;
  @body page: PageResponse;
} | AllErrorResponse;

@post
op createPage(@body page: CreatePageResult): {
  @statusCode statusCode: 200;
  @body createdPage: PageResponse;
} | AllErrorResponse;

@patch(#{ implicitOptionality: true })
op updatePage(@path pageId: int32, @body page: UpdatePageRequest): {
  @statusCode statusCode: 200;
  @body updatedPage: PageResponse;
} | AllErrorResponse;

@delete
op deletePage(@path pageId: int32): {
  @statusCode statusCode: 204;
} | AllErrorResponse;
