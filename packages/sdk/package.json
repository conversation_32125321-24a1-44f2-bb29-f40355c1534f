{"name": "@saf/sdk", "version": "0.1.0", "type": "module", "private": true, "scripts": {"compile": "tsp compile ./src/main.tsp", "watch": "tsp compile --watch ./src/main.tsp", "generate-client": "npx openapi-typescript ./tsp-output/schema/openapi.yaml -o ./openapi-ts-output/schema.d.ts && prettier --write ./openapi-ts-output/schema.d.ts", "test:ts": "tsc --noEmit ./src/index.ts", "build": "pnpm compile && pnpm generate-client"}, "peerDependencies": {"@typespec/compiler": "latest", "@typespec/http": "latest", "@typespec/openapi": "latest", "@typespec/openapi3": "latest", "@typespec/rest": "latest"}, "devDependencies": {"@typespec/compiler": "latest", "@typespec/http": "latest", "@typespec/openapi": "latest", "@typespec/openapi3": "latest", "@typespec/rest": "latest", "openapi-typescript": "^7.8.0", "typescript": "^5.8.3"}, "packageManager": "pnpm@9.4.0", "exports": {".": "./src/index.ts"}}